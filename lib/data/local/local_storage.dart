import 'package:family_app/config/constant/app_config.dart';
import 'package:family_app/config/service/language_service.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';

const String keyAccessToken = "_accessToken";
const String keyRefreshToken = "_refreshToken";
const String keyUserId = '_userId';
const String keyUserRole = '_userId';
const String keyLanguageCode = '_languageCode';
const String keyPermissions = '_permission';
const String keyFirstUserApp = '_firstUseApp';
const String keyVerificationId = '_verificationId';
const String keyResendToken = '_resendToken';
const String keyUID = '_uid';
const String keyRefCode = '_refCode';
const String keyEmail = '_email';
const String keyPassword = '_password';
const String keyLoggedIn = '_is_logged_in';

//Calendar
const String _enableCalendarSyncKey = "enableCalendarSyncKey";
const String _startOfWeekMondayKey = "startOfWeekMondayKey";
const String _defaultReminderKeyKey = "defaultReminderKey";
const String _enabledCalendarIdsKey = "enabledCalendarIdsKey";
const String _eventInfoListKey = "eventInfoListKey";

//Amadeus
const String keyAmadeusAccessToken = "_amadeusAccessToken";
const String keyAmadeusTokenExpired = "_amadeusTokenExpired";

abstract class LocalStorage {
  Future<void> cacheAccessToken(String token);

  Future<void> cacheRefreshToken(String refreshToken);

  Future<void> cacheUserID(String userId);

  Future<void> cacheUserRole(String userRole);

  Future<void> cacheLanguageCode(String language);

  Future<void> cacheGroupPermission(List<String> permission);

  Future<void> cacheVerificationId(String verificationId);

  Future<void> cacheResendToken(int? resendToken);

  Future<void> cacheUID(String uid);

  Future<void> cacheRefCode(String refCode);

  Future<void> cacheEmail(String email);

  Future<void> cachePassword(String password);

  Future<void> cacheLoggedIn(bool isLoggedIn);

  Future<void> cacheSyncWithDeviceCalendar(bool isSync);

  Future<void> cacheStartOfWeekMonday(bool isStartOfWeekMonday);

  Future<void> cacheDefaultReminder(int defaultReminder);

  /// Save enabled calendar IDs (as a list of strings)
  Future<void> cacheEnabledCalendarIds(Set<String> ids);

  /// Get enabled calendar IDs (as a set of strings)
  Set<String> get enabledCalendarIds;

  /// Store event info (uuid, calendarId, familyId)
  Future<void> cacheEventInfoList(List<Map<String, String>> eventInfoList);

  /// Retrieve event info list
  List<Map<String, String>> get eventInfoList;

  Future<String?> accessToken();

  Future<String?> refreshToken();

  Future<String?> userId();

  bool loggedIn();

  Future<String> getEmail();

  Future<String> getPassword();

  /// Amadeus
  Future<void> cacheAmadeusAccessToken(String token);

  Future<void> cacheAmadeusTokenExpired(String expired);

  String? get amadeusAccessToken;

  String? get amadeusTokenExpired;

  List<String> get permission;

  String get languageCode;

  String get userRole;

  String get verificationId;

  int? get resendToken;

  String get refCode;

  String get uid;

  bool get isSyncWithDeviceCalendar;

  bool get isStartOfWeekMonday;

  int get defaultReminder;

  Future<void> setString(String key, String value);

  Future<String?> getString(String key, {String? defaultValue});

  Future<void> clear();
}

@Singleton(as: LocalStorage)
class LocalStorageImpl extends LocalStorage {
  late final FlutterSecureStorage _flutterSecureStorage;
  late final SharedPreferences sharedPreferences;

  LocalStorageImpl();

  @PostConstruct(preResolve: true)
  Future<void> onInitService() async {
    _flutterSecureStorage = const FlutterSecureStorage(aOptions: AndroidOptions(encryptedSharedPreferences: true), iOptions: IOSOptions(accessibility: KeychainAccessibility.first_unlock));

    sharedPreferences = await SharedPreferences.getInstance();
    final isFirstUes = sharedPreferences.getBool(keyFirstUserApp) ?? false;
    if (!isFirstUes) {
      sharedPreferences.setBool(keyFirstUserApp, true);
      await _flutterSecureStorage.deleteAll();
    }
  }

  @override
  Future<void> cacheAccessToken(String token) async {
    sharedPreferences.setString(keyAccessToken, token);
    return await _flutterSecureStorage.write(key: keyAccessToken, value: token);
  }

  @override
  Future<void> cacheRefreshToken(String refreshToken) async {
    return await _flutterSecureStorage.write(key: keyRefreshToken, value: refreshToken);
  }

  @override
  Future<void> clear() {
    sharedPreferences.remove(_enabledCalendarIdsKey);
    sharedPreferences.clear();
    sharedPreferences.setBool(keyFirstUserApp, true);
    return _flutterSecureStorage.deleteAll();
  }

  @override
  Future<String?> accessToken() async {
    return await _flutterSecureStorage.read(key: keyAccessToken);
  }

  @override
  Future<String?> refreshToken() async {
    return await _flutterSecureStorage.read(key: keyRefreshToken);
  }

  @override
  Future<void> cacheUserID(String userId) {
    return _flutterSecureStorage.write(key: keyUserId, value: userId);
  }

  @override
  Future<String?> userId() {
    return _flutterSecureStorage.read(key: keyUserId);
  }

  @override
  Future<void> cacheUserRole(String userRole) {
    return sharedPreferences.setString(keyUserRole, userRole);
  }

  @override
  String get userRole => sharedPreferences.getString(keyUserRole) ?? '';

  @override
  Future<void> cacheLanguageCode(String language) async {
    await sharedPreferences.setString(keyLanguageCode, language);
  }

  @override
  String get languageCode => sharedPreferences.getString(keyLanguageCode) ?? vietnamCode;

  @override
  Future<void> cacheGroupPermission(List<String> permission) async {
    await sharedPreferences.setStringList(keyPermissions, permission);
  }

  @override
  List<String> get permission => sharedPreferences.getStringList(keyPermissions) ?? [];

  @override
  Future<void> cacheResendToken(int? resendToken) async {
    if (resendToken == null) {
      sharedPreferences.remove(keyRefreshToken);
      return;
    }
    await sharedPreferences.setInt(keyRefreshToken, resendToken);
  }

  @override
  Future<void> cacheVerificationId(String verificationId) async {
    await sharedPreferences.setString(keyVerificationId, verificationId);
  }

  @override
  int? get resendToken => sharedPreferences.getInt(keyRefreshToken);

  @override
  String get verificationId => sharedPreferences.getString(keyVerificationId) ?? '';

  @override
  Future<void> cacheUID(String uid) async {
    await sharedPreferences.setString(keyUID, uid);
  }

  @override
  Future<void> cacheRefCode(String refCode) async {
    await sharedPreferences.setString(keyRefCode, refCode);
  }

  @override
  String get refCode => sharedPreferences.getString(keyRefCode) ?? '';

  @override
  String get uid => sharedPreferences.getString(keyUID) ?? '';

  @override
  Future<void> cachePassword(String password) => _flutterSecureStorage.write(key: keyPassword, value: password);

  @override
  Future<String> getPassword() async => (await _flutterSecureStorage.read(key: keyPassword) ?? '');

  @override
  Future<void> cacheLoggedIn(bool isLoggedIn) async {
    await sharedPreferences.setBool(keyLoggedIn, isLoggedIn);
  }

  @override
  bool loggedIn() {
    return sharedPreferences.getBool(keyLoggedIn) ?? false;
  }

  @override
  Future<void> cacheEmail(String email) => _flutterSecureStorage.write(key: keyEmail, value: email);

  @override
  Future<String> getEmail() async => (await _flutterSecureStorage.read(key: keyEmail) ?? '');

  /// Calendar Sync
  @override
  Future<void> cacheSyncWithDeviceCalendar(bool isSync) async {
    await sharedPreferences.setBool(_enableCalendarSyncKey, isSync);
  }

  @override
  bool get isSyncWithDeviceCalendar => sharedPreferences.getBool(_enableCalendarSyncKey) ?? false;

  /// Start of Week Monday
  @override
  Future<void> cacheStartOfWeekMonday(bool isStartOfWeekMonday) async {
    await sharedPreferences.setBool(_startOfWeekMondayKey, isStartOfWeekMonday);
  }

  @override
  bool get isStartOfWeekMonday => sharedPreferences.getBool(_startOfWeekMondayKey) ?? false;

  /// Default Reminder
  @override
  Future<void> cacheDefaultReminder(int defaultReminder) async {
    await sharedPreferences.setInt(_defaultReminderKeyKey, defaultReminder);
  }

  @override
  Future<void> cacheEnabledCalendarIds(Set<String> ids) async {
    await sharedPreferences.setStringList(_enabledCalendarIdsKey, ids.toList());
  }

  @override
  Set<String> get enabledCalendarIds {
    final list = sharedPreferences.getStringList(_enabledCalendarIdsKey) ?? [];
    return list.toSet();
  }

  @override
  Future<void> cacheEventInfoList(List<Map<String, String>> eventInfoList) async {
    // Store as list of JSON strings
    final jsonList = eventInfoList.map((e) => e.toString()).toList();
    await sharedPreferences.setStringList(_eventInfoListKey, jsonList);
  }

  @override
  List<Map<String, String>> get eventInfoList {
    final jsonList = sharedPreferences.getStringList(_eventInfoListKey) ?? [];
    // Parse each string back to Map<String, String>
    return jsonList.map<Map<String, String>>((str) {
      // Remove curly braces and split by comma
      final map = <String, String>{};
      str.replaceAll('{', '').replaceAll('}', '').split(', ').forEach((pair) {
        final kv = pair.split(': ');
        if (kv.length == 2) {
          map[kv[0]] = kv[1];
        }
      });
      return map;
    }).toList();
  }

  @override
  int get defaultReminder => sharedPreferences.getInt(_defaultReminderKeyKey) ?? 0;

  //Amadeus
  @override
  Future<void> cacheAmadeusAccessToken(String token) async {
    await sharedPreferences.setString("${AppConfig.AMADEUS_API_SECRET}$keyAmadeusAccessToken", token);
  }

  @override
  Future<void> cacheAmadeusTokenExpired(String expired) async {
    await sharedPreferences.setString("${AppConfig.AMADEUS_API_SECRET}$keyAmadeusTokenExpired", expired);
  }

  @override
  String? get amadeusAccessToken => sharedPreferences.getString("${AppConfig.AMADEUS_API_SECRET}$keyAmadeusAccessToken");

  @override
  String? get amadeusTokenExpired => sharedPreferences.getString("${AppConfig.AMADEUS_API_SECRET}$keyAmadeusTokenExpired");

  @override
  Future<void> setString(String key, String value) async {
    await sharedPreferences.setString(key, value);
  }

  @override
  Future<String?> getString(String key, {String? defaultValue}) async {
    return sharedPreferences.getString(key) ?? defaultValue;
  }
}
