import 'package:family_app/data/model/hotel_booking_model.dart';
import 'package:family_app/data/model/hotel_model.dart';
import 'package:family_app/data/model/transfer_model.dart';
import 'package:family_app/data/usecase/model/activity_parameter.dart';
import 'package:family_app/utils/content_provider.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

part 'trip_model.g.dart';

@JsonSerializable(explicitToJson: true)
class Trip {
  @Json<PERSON>ey(name: 'name')
  String name;
  @JsonKey(name: 'is_date_confirmed')
  bool isDateConfirmed;
  @JsonKey(name: 'from_date')
  DateTime fromDate;
  @Json<PERSON>ey(name: 'to_date')
  DateTime toDate;
  @JsonKey(name: 'country')
  String? country;
  @JsonKey(name: 'city')
  String? city;
  @JsonKey(name: 'gps_coord')
  List<double>? gpsCoord; // GPS coordinates for the main city of the trip

  @JsonKey(name: 'color')
  String? color;
  @Json<PERSON>ey(name: 'description')
  String? description;
  @JsonKey(name: 'included_events')
  List<dynamic>? includedEvents;
  @JsonKey(name: 'itinerary')
  List<Itinerary> itinerary;
  @JsonKey(name: 'family_id')
  String? familyId;
  @JsonKey(name: 'uuid')
  String? uuid; // will be set for existing trips - used to update the trip

  @JsonKey(name: 'tripImageUrl')
  String? tripImageUrl;

  @JsonKey(name: 'hotel_preferences')
  HotelPreferences? hotelPreferences;
  @JsonKey(name: 'flight_preferences')
  FlightPreferences? flightPreferences;
  @JsonKey(includeFromJson: false, includeToJson: false)
  List<HotelBookingModel>? selectedHotels;

  @JsonKey(name: 'city_code')
  String? cityCode;
  @JsonKey(name: 'additional_cities')
  List<String>? additionalCities;
  @JsonKey(name: 'additional_city_code')
  List<String>? additionalCityCode;

  Trip(
      {required this.name,
      required this.isDateConfirmed,
      required this.fromDate,
      required this.toDate,
      required this.country,
      required this.city,
      required this.color,
      required this.description,
      required this.itinerary,
      required this.familyId,
      required this.uuid,
      this.hotelPreferences,
      this.flightPreferences,
      this.selectedHotels,
      this.cityCode,
      this.additionalCities,
      this.additionalCityCode,
      this.gpsCoord});

  factory Trip.fromJson(Map<String, dynamic> json) => _$TripFromJson(json);
  Map<String, dynamic> toJson() => _$TripToJson(this);

  static Future<void> fetchImagesAndLocations(
      List<Itinerary> itineraries, ContentProvider provider) async {
    for (var itinerary in itineraries) {
      if (itinerary.accommodation != null && itinerary.activities != null) {
        itinerary.imageUrl =
            await provider.fetchImageUrl(itinerary.activities![0].description);

        //if imageUrl is not null and empty fetch the accommodation image
        if (itinerary.imageUrl == null || itinerary.imageUrl!.isEmpty) {
          itinerary.imageUrl =
              await provider.fetchImageUrl(itinerary.accommodation ?? '');
        }

        print(
            "fetching location details for: ${itinerary.accommodation} \n image url: ${itinerary.imageUrl}");
        //TODO: Think about this later.
        // itinerary.locationDetails =
        // await provider.fetchLocationDetails(itinerary.accommodation);
      } else {
        print("empty accommodation for: $itinerary");
      }
    }
  }

  // Method to convert Trip to Map
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'is_date_confirmed': isDateConfirmed,
      'from_date': fromDate.toIso8601String(),
      'to_date': toDate.toIso8601String(),
      'country': country,
      'city': city,
      'gps_coord': gpsCoord,
      'color': color,
      'description': description,
      'included_events': includedEvents,
      'itinerary': itinerary.map((itinerary) => itinerary.toMap()).toList(),
      'activity_type': 'trip',
      'family_id': familyId,
      'uuid': uuid,
      'city_code': cityCode,
      'additional_cities': additionalCities,
      'additional_city_code': additionalCityCode,
    };
  }

  // Method to convert Trip to CreateActivityParameter
  CreateActivityParameter toCreateActivityParameter() {
    return CreateActivityParameter(
        name: name,
        isDateConfirmed: isDateConfirmed,
        fromDate: fromDate.toIso8601String(),
        toDate: toDate.toIso8601String(),
        caption: '',
        country: country,
        city: city,
        gpsCoord: gpsCoord,
        color: color,
        description: description,
        includedEvents: includedEvents,
        itinerary: itinerary,
        activityType: 'trip',
        familyId: familyId ?? '',
        uuid: uuid,
        tripImageUrl: tripImageUrl,
        hotelPreferences: hotelPreferences,
        flightPreferences: flightPreferences,
        cityCode: cityCode ?? '',
        additionalCities: additionalCities ?? [],
        additionalCityCode: additionalCityCode ?? []);
  }
}

@JsonSerializable(explicitToJson: true)
class Itinerary {
  @JsonKey(name: 'accommodation')
  String? accommodation;
  @JsonKey(name: 'activities')
  List<Activity>? activities;
  @JsonKey(name: 'transfers')
  List<TransferModel>? transfers;
  // @JsonKey(name: 'food')
  // List<String>? food;
  @JsonKey(name: 'imageUrl')
  String? imageUrl;
  @JsonKey(name: 'foodAndUrl')
  Map<String, String?>? food;

  Itinerary({
    this.accommodation,
    this.activities,
    this.food,
    this.imageUrl,
    // this.foodImageUrl,
  });

  factory Itinerary.fromJson(Map<String, dynamic> json) =>
      _$ItineraryFromJson(json);

  Map<String, dynamic> toJson() => _$ItineraryToJson(this);

  // factory Itinerary.fromJson(Map<String, dynamic> json) {
  //   return Itinerary(
  //     accommodation: json['accommodation'] ?? '',
  //     activities:
  //         json['activities'] != null ? List<Activity>.from(json['activities'].map((x) => Activity.fromJson(x))) : [],
  //     food: json['food'] != null ? List<String>.from(json['food']) : [],
  //     imageUrl: json['imageUrl'] ?? '',
  //     // locationDetails: json['locationDetails'],
  //   );
  // }

  // Method to convert Itinerary to Map
  Map<String, dynamic> toMap() {
    return {
      'accommodation': accommodation,
      'activities': activities?.map((activity) => activity.toMap()).toList(),
      'food': food,
      'imageUrl': imageUrl,
      // 'foodImageUrl': foodImageUrl,
    };
  }

  @override
  String toString() {
    return 'Itinerary: {accommodation: $accommodation, activities: $activities, food: $food, imageUrl: $imageUrl}';
  }
}

@JsonSerializable(explicitToJson: true)
class Activity {
  //Day Activity - not the Big TRIP kind of activity
  @JsonKey(name: 'time')
  String time; // Time in HH:mm format, e.g., "14:30"
  @JsonKey(name: 'duration')
  int? duration; // Duration in Minute

  @JsonKey(name: 'description')
  String description;

  @JsonKey(name: 'venue')
  String? venue;
  @JsonKey(name: 'activityImage')
  String? activityImage;
  @JsonKey(name: 'city')
  String? city;
  @JsonKey(name: 'gpsCoord')
  List<double>? gpsCoord;
  @JsonKey(name: 'category')
  String? category;
  @JsonKey(name: 'latitude')
  double? latitude;
  @JsonKey(name: 'longitude')
  double? longitude;

  @JsonKey(name: 'uuid')
  String? uuid; // Unique identifier for the activity, if needed

  Activity({
    required this.time,
    required this.description,
    required this.venue,
    this.uuid,
    this.activityImage,
    this.city,
    this.gpsCoord,
    this.category,
    this.latitude,
    this.longitude,
    this.duration = 0,
  });

  factory Activity.fromJson(Map<String, dynamic> json) =>
      _$ActivityFromJson(json);

  Map<String, dynamic> toJson() => _$ActivityToJson(this);

  // Method to convert Activity to Map
  Map<String, dynamic> toMap() {
    return {
      'uuid': uuid,
      'time': time,
      'duration': duration,
      'description': description,
      'venue': venue,
      'activityImage': activityImage,
      'city': city,
      'gpsCoord': gpsCoord,
      'category': category,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  @override
  String toString() {
    return 'Activity: {time: $time, duration: $duration, description: $description, venue: $venue, activityImage: $activityImage, city: $city, gpsCoord: $gpsCoord, latitude: $latitude, longitude: $longitude}';
  }
}

class ActivityTimelineItem {
  final DateTime dateTime;
  final dynamic data; // ActivityModel or TransferModel

  ActivityTimelineItem({required this.dateTime, required this.data});
}

@JsonSerializable(explicitToJson: true)
class HotelPreferences {
  @JsonKey(name: 'location')
  String? location;
  @JsonKey(name: 'check_in_date')
  DateTime? checkInDate;
  @JsonKey(name: 'check_out_date')
  DateTime? checkOutDate;
  @JsonKey(name: 'number_of_guests')
  int? numberOfGuests;
  @JsonKey(name: 'room_type')
  String? roomType;
  @JsonKey(name: 'star_rating')
  int? starRating;
  @JsonKey(name: 'amenities')
  List<String>? amenities;
  @JsonKey(name: 'budget')
  Budget? budget;

  HotelPreferences({
    this.location,
    this.checkInDate,
    this.checkOutDate,
    this.numberOfGuests,
    this.roomType,
    this.starRating,
    this.amenities,
    this.budget,
  });

  factory HotelPreferences.fromJson(Map<String, dynamic> json) =>
      _$HotelPreferencesFromJson(json);
  Map<String, dynamic> toJson() => _$HotelPreferencesToJson(this);
}

@JsonSerializable(explicitToJson: true)
class FlightPreferences {
  @JsonKey(name: 'departure_airport_name')
  String? departureAirportName;
  @JsonKey(name: 'depature_airport_code')
  String? departureAirportCode;
  @JsonKey(name: 'arrival_airport_name')
  String? arrivalAirportName;
  @JsonKey(name: 'arrival_airport_code')
  String? arrivalAirportCode;
  @JsonKey(name: 'departure_date')
  DateTime? departureDate;
  @JsonKey(name: 'return_date')
  DateTime? returnDate;
  @JsonKey(name: 'number_of_passengers')
  int? numberOfPassengers;
  @JsonKey(name: 'cabin_class')
  String? cabinClass;
  @JsonKey(name: 'preferred_airlines')
  List<String>? preferredAirlines;
  @JsonKey(name: 'budget')
  Budget? budget;

  FlightPreferences({
    this.departureAirportName,
    this.departureAirportCode,
    this.arrivalAirportName,
    this.arrivalAirportCode,
    this.departureDate,
    this.returnDate,
    this.numberOfPassengers,
    this.cabinClass,
    this.preferredAirlines,
    this.budget,
  });

  factory FlightPreferences.fromJson(Map<String, dynamic> json) =>
      _$FlightPreferencesFromJson(json);
  Map<String, dynamic> toJson() => _$FlightPreferencesToJson(this);
}

@JsonSerializable(explicitToJson: true)
class Budget {
  @JsonKey(name: 'min')
  int? min;
  @JsonKey(name: 'max')
  int? max;

  Budget({
    this.min,
    this.max,
  });

  factory Budget.fromJson(Map<String, dynamic> json) => _$BudgetFromJson(json);
  Map<String, dynamic> toJson() => _$BudgetToJson(this);
}
