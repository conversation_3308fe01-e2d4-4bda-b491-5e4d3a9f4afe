import 'dart:async';
import 'dart:io';

import 'package:family_app/utils/log/app_logger.dart';
import 'package:auto_route/auto_route.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/stream/base_stream_builder.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/service/language_service.dart';
import 'package:family_app/config/theme/app_theme_util.dart';
import 'package:family_app/config/theme/base_theme_data.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/utils/reponsive/size_config.dart';
import 'package:family_app/utils/reponsive/size_config_v2.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:path_provider/path_provider.dart';

import 'bootstrap.dart';
import 'firebase_options.dart';

AppThemeUtil themeUtil = AppThemeUtil();
BaseThemeData get appTheme => themeUtil.getAppTheme();

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

/// Router
final appRouter = AppRouter();
final RouteObserver<PageRoute> routeObserver = RouteObserver<PageRoute>();
GlobalKey<NavigatorState> get navigatorKey => appRouter.navigatorKey;

const isDebug = true;

const gPlatform = MethodChannel('flutter.native/log_helper');

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await dotenv.load(fileName: '.env');
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  await configureDependencies();

  // Initialize AppLogger file logging to the same log directory as LogService
  final appDocDir = await getApplicationDocumentsDirectory();
  final logDir = Directory('${appDocDir.path}/logs');
  if (!await logDir.exists()) {
    await logDir.create(recursive: true);
  }
  await AppLogger.initFileLogging(logDir);

  // Log device/app info at startup
  logi('App started. Device: ${Platform.operatingSystem} ${Platform.operatingSystemVersion}');

  // Log app lifecycle events
  WidgetsBinding.instance.addObserver(_AppLifecycleLogger());

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]);

  // Redirect debugPrint to AppLogger
  debugPrint = (String? message, {int? wrapWidth}) {
    if (message != null) {
      logd('[Flutter] ' + message);
    }
  };

  // Capture Flutter framework errors
  FlutterError.onError = (FlutterErrorDetails details) {
    loge('[FlutterError] ' + details.exceptionAsString(), null, details.stack);
    FirebaseCrashlytics.instance.recordFlutterFatalError(details);
  };

  // Capture uncaught Dart errors
  PlatformDispatcher.instance.onError = (Object error, StackTrace stack) {
    loge('[Uncaught Dart Error] ' + error.toString(), null, stack);
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    return true;
  };

  if (!kDebugMode) {
    // write all log to file for exporting later in Release mode
    gPlatform.invokeMethod<List>('initLogToFile');
  }
  // try {
  //   // await NotificationHelper.initialize();
  //   // FirebaseMessaging.onBackgroundMessage(myBackgroundMessageHandler);
  // } catch (_) {}

  runZonedGuarded(() {
    bootstrap(() {
      return LayoutBuilder(
        builder: (context, constraints) {
          SizeConfig.instance.init(constraints: constraints, screenHeight: 812, screenWidth: 375);
          SizeConfigV2.instance.init(constraints: constraints, screenHeight: 870.46400, screenWidth: 402);

          return EasyLocalization(
            supportedLocales: supportedLocales,
            path: 'assets/lang',
            fallbackLocale: english,
            startLocale: english,
            child: const MainAppPage(),
          );
        },
      );
    });
  }, (error, stack) {
    loge('[Uncaught Dart Error] ' + error.toString(), null, stack);
  });
}
// App lifecycle logger
class _AppLifecycleLogger extends WidgetsBindingObserver {
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    logi('[LIFECYCLE] App lifecycle state changed: $state');
  }
}

class MainAppPage extends StatefulWidget {
  const MainAppPage({super.key});

  static MainAppPageState of(BuildContext context) {
    final state = context.findAncestorStateOfType<MainAppPageState>();

    if (state == null) {
      throw Exception('The state still not init');
    }

    return state;
  }

  @override
  State<MainAppPage> createState() => MainAppPageState();
}

class MainAppPageState extends State<MainAppPage> {
  final languageService = locator.get<LanguageService>();

  void onSwitchLightDarkMode() {
    themeUtil.onChangeLightDarkMode();
  }

  @override
  void dispose() {
    themeUtil.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ThreeBaseStreamBuilder(
      firstController: languageService.language,
      secondController: SizeConfig.instance.isLandscape,
      thirdController: SizeConfigV2.instance.isLandscape,
      builder: (language, landscape, landscapeV2) => ValueListenableBuilder(
          valueListenable: themeUtil.themeType,
          builder: (context, type, child) {
            return MaterialApp.router(
              locale: context.locale,
              localizationsDelegates: context.localizationDelegates,
              supportedLocales: context.supportedLocales,
              debugShowCheckedModeBanner: false,
              theme: themeUtil.theme.getThemeData(type),
              routerConfig: appRouter.config(navigatorObservers: () => [routeObserver, ...AutoRouterDelegate.defaultNavigatorObserversBuilder()]),
              builder: EasyLoading.init(
                builder: (context, child) => child!,
              ),
            );
          }),
    );
  }
}
