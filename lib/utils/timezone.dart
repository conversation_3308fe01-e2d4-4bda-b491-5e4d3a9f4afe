import 'package:family_app/data/model/timezone.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:timezone/data/latest.dart' as tz_data;
import 'package:timezone/timezone.dart' as tz;
import 'package:collection/collection.dart';
import 'package:family_app/config/constant/timezone.dart' as tz_const;

enum TimezoneOffsetFormat { gmt, utc, iso, z }

/// TimeZoneUtils provides utilities for working with timezones, including
/// initialization, conversion, and formatting. It supports IANA, Windows, and
/// GMT offset formats, and integrates with the Flutter timezone package.
class TimeZoneUtils {
  // --- Core constants ---
  static const String kDefaultTimeZone = 'UTC';
  static const String kUtcIana = 'Etc/UTC';
  static const String kUtcAbbr = 'UTC';
  static const String kUtcOffset = 'GMT+00:00';
  static const String kDefaultWindowsTimeZone = 'UTC';
  static const String kGmtFormat = 'GMT'; // Used as prefix for GMT offset
  static const String kDefaultTimeFormat = 'hh:mm';
  static const String kDefaultDateFormat = 'dd/MM/yyyy';

  // --- Aliases and mappings ---
  static const Map<String, String> timezoneAliases = tz_const.timezoneAliases;
  static const List<Map<String, String>> _sfCalendarTimeZoneTable = tz_const.sfCalendarTimeZoneTable;

  static final List<Timezone> _uniqueTimezoneList = _buildUniqueTimezoneList();

  // --- Initialization & Core ---
  /// Initializes timezone database and sets the local timezone for the app.
  ///
  /// Example:
  /// ```dart
  /// await TimeZoneUtils.initializeTimeZone();
  /// ```
  static Future<void> initializeTimeZone() async {
    try {
      tz_data.initializeTimeZones();
      String deviceTimeZone = await getCurrentLocalTimeZone();
      print('[TZ_INIT] Device timezone from flutter_native_timezone: $deviceTimeZone');
      tz.setLocalLocation(tz.getLocation(deviceTimeZone));
      print('[TZ_INIT] tz.local after setLocalLocation: ${tz.local.name}');
    } catch (e, stack) {
      print('[TZ_INIT][ERROR] Failed to initialize timezone: $e\n$stack');
    }
  }

  /// Builds a list of unique [Timezone] objects, one per distinct GMT offset.
  ///
  /// Each [Timezone] in the list has:
  ///   - [name]: IANA timezone name, e.g. 'Asia/Ho_Chi_Minh'
  ///   - [abbr]: Abbreviation or city, e.g. 'ICT'
  ///   - [offset]: Offset in GMT format, e.g. 'GMT+07:00'
  ///
  /// Example output:
  /// ```dart
  /// [
  ///   Timezone('Asia/Ho_Chi_Minh', 'ICT', 'GMT+07:00'),
  ///   Timezone('Europe/London', 'BST', 'GMT+01:00'),
  ///   Timezone('Etc/UTC', 'UTC', 'GMT+00:00'),
  ///   // ...
  /// ]
  /// ```
  static List<Timezone> _buildUniqueTimezoneList() {
    final seenOffsets = <String>{};
    final timezones = <Timezone>[];
    for (final entry in tz.timeZoneDatabase.locations.entries) {
      final name = entry.key;
      final location = entry.value;
      final now = tz.TZDateTime.now(location);
      String abbr = now.timeZoneName;
      if (abbr.isEmpty) {
        final segments = name.split('/');
        abbr = segments.isNotEmpty ? segments.last.replaceAll('_', ' ') : name;
      }
      if (abbr.isEmpty) {
        abbr = formatTimezoneOffset(now.timeZoneOffset);
      }
      final offsetString = formatTimezoneOffset(now.timeZoneOffset);
      if (!seenOffsets.contains(offsetString)) {
        seenOffsets.add(offsetString);
        timezones.add(Timezone(
          name,
          abbr,
          offsetString,
        ));
      }
    }
    timezones.sort((a, b) => a.offset.compareTo(b.offset));
    return timezones;
  }

  /// Returns a list of unique Timezone models by GMT offset, for calendar selection.
  ///
  /// Example:
  /// ```dart
  /// List<Timezone> tzList = TimeZoneUtils.getUniqueTimezoneList();
  /// ```
  static List<Timezone> getUniqueTimezoneList() => _uniqueTimezoneList;

  // --- Timezone Info & Conversion ---
  /// Gets the current device timezone as an IANA name, applying alias mapping if needed.
  ///
  /// Returns: e.g. 'Asia/Ho_Chi_Minh'
  ///
  /// Example:
  /// ```dart
  /// String tz = await TimeZoneUtils.getCurrentLocalTimeZone();
  /// // tz == 'Asia/Ho_Chi_Minh'
  /// ```
  static Future<String> getCurrentLocalTimeZone() async {
    String deviceTimeZone = await FlutterTimezone.getLocalTimezone();
    return timezoneAliases[deviceTimeZone] ?? deviceTimeZone;
  }

  /// Returns the mapped Windows time zone ID for Syncfusion SfCalendar.
  ///
  /// Returns: e.g. 'SE Asia Standard Time'
  ///
  /// Example:
  /// ```dart
  /// String winTz = await TimeZoneUtils.getSfCalendarTimeZone();
  /// // winTz == 'SE Asia Standard Time'
  /// ```
  static Future<String> getSfCalendarTimeZone() async {
    String deviceTimeZone = await FlutterTimezone.getLocalTimezone();
    deviceTimeZone = timezoneAliases[deviceTimeZone] ?? deviceTimeZone;

    // Use resolveToIana to get a canonical IANA timezone name
    final resolvedIana = resolveToIana(deviceTimeZone);

    // Use the helper for IANA -> Windows mapping
    final winTz = lookupWindowsFromIana(resolvedIana);
    if (winTz != null) {
      return winTz;
    }

    // Try lookup in Syncfusion's supported table by IANA or offset
    if (tz.timeZoneDatabase.locations.containsKey(resolvedIana)) {
      final location = tz.getLocation(resolvedIana);
      final now = tz.TZDateTime.now(location);
      final offset = formatTimezoneOffset(now.timeZoneOffset, type: TimezoneOffsetFormat.gmt);
      final winTz2 = _lookupSfCalendarWindowsTimeZone(resolvedIana, offset);
      if (winTz2 != null) {
        return winTz2;
      }
    }

    // As a last resort, use UTC (per Syncfusion docs)
    return kDefaultWindowsTimeZone;
  }

  /// Converts a Windows time zone ID to IANA time zone name.
  ///
  /// Example:
  /// ```dart
  /// String? iana = TimeZoneUtils.windowsToIana('SE Asia Standard Time');
  /// // iana == 'Asia/Ho_Chi_Minh'
  /// ```
  static String? windowsToIana(String windowsTz) {
    return lookupIanaFromWindows(windowsTz);
  }

  /// Resolves a timezone string (IANA, Windows, or GMT offset) to a valid IANA timezone name for tz.getLocation.
  ///
  /// Example:
  /// ```dart
  /// String iana = TimeZoneUtils.resolveToIana('SE Asia Standard Time');
  /// // iana == 'Asia/Ho_Chi_Minh'
  /// ```
  static String resolveToIana(String? timeZoneOrOffset, {String? fallbackWindowsTz}) {
    if (timeZoneOrOffset == null || timeZoneOrOffset.isEmpty) {
      // Try fallback Windows timezone if provided
      if (fallbackWindowsTz != null) {
        final iana = windowsToIana(fallbackWindowsTz);
        if (iana != null) return iana;
      }
      return kDefaultTimeZone;
    }

    // If it's a known alias, map it
    if (timezoneAliases.containsKey(timeZoneOrOffset)) {
      return timezoneAliases[timeZoneOrOffset]!;
    }

    // If it's a known IANA, return as is
    if (tz.timeZoneDatabase.locations.containsKey(timeZoneOrOffset)) {
      return timeZoneOrOffset;
    }

    // Use the helper for Windows -> IANA mapping
    final ianaFromWindows = windowsToIana(timeZoneOrOffset);
    if (ianaFromWindows != null) {
      return ianaFromWindows;
    }

    // If it's a GMT/UTC offset or Etc/GMT±X, try to find a matching IANA timezone
    // See: https://help.syncfusion.com/flutter/calendar/timezone
    // "Etc/GMT-5" (POSIX) means UTC+5, so we map to a real IANA zone with that offset
    if (timeZoneOrOffset.startsWith('Etc/GMT') || timeZoneOrOffset.startsWith('Etc/UTC')) {
      if (tz.timeZoneDatabase.locations.containsKey(timeZoneOrOffset)) {
        return timeZoneOrOffset;
      }
      // Try to parse the offset and find a matching IANA timezone by offset
      final match = RegExp(r'Etc/GMT([+-]\d+)').firstMatch(timeZoneOrOffset);
      if (match != null) {
        // POSIX sign is reversed: Etc/GMT-5 means UTC+5
        final offsetHours = int.tryParse(match.group(1)!);
        if (offsetHours != null) {
          final offset = Duration(hours: -offsetHours); // reverse sign
          final gmtString = formatTimezoneOffset(offset, type: TimezoneOffsetFormat.gmt);
          // Try to find a real IANA timezone with this offset (not Etc/GMT±X)
          final found = _uniqueTimezoneList.firstWhereOrNull((tz) => tz.offset == gmtString && !tz.name.startsWith('Etc/GMT'));
          if (found != null) {
            return found.name;
          }
          // fallback: return the Etc/GMT±X if exists
          if (tz.timeZoneDatabase.locations.containsKey('Etc/GMT${match.group(1)}')) {
            return 'Etc/GMT${match.group(1)}';
          }
        }
      }
    }

    // If it's a GMT offset, try to find a matching timezone (use cached list)
    if (timeZoneOrOffset.startsWith(kGmtFormat)) {
      final found = _uniqueTimezoneList.where((tz) => tz.offset == timeZoneOrOffset && !tz.name.startsWith('Etc/GMT'));
      if (found.isNotEmpty) {
        return found.first.name;
      }
    }

    // Fallback to UTC
    return kDefaultTimeZone;
  }

  // --- DateTime Conversion ---
  /// Converts a DateTime to the specified Timezone (using Timezone model).
  ///
  /// Example:
  /// ```dart
  /// DateTime utc = DateTime.utc(2024, 1, 1, 12, 0);
  /// Timezone tz = Timezone('Asia/Ho_Chi_Minh', 'ICT', 'GMT+07:00');
  /// DateTime local = TimeZoneUtils.convertDateTimeToTimezone(utc, tz);
  /// // local is 2024-01-01 19:00:00.000
  /// ```
  static DateTime convertDateTimeToTimezone(DateTime dateTime, Timezone timezone) {
    try {
      final location = tz.getLocation(timezone.name);
      final utcDateTime = dateTime.isUtc ? dateTime : dateTime.toUtc();
      final converted = tz.TZDateTime.from(utcDateTime, location);
      return converted;
    } catch (e, stack) {
      print('[TZ_CONVERT][ERROR] Failed to convert dateTime: $e\n$stack');
      return dateTime;
    }
  }

  /// Converts a DateTime to the specified IANA timezone name.
  ///
  /// Example:
  /// ```dart
  /// DateTime utc = DateTime.utc(2024, 1, 1, 12, 0);
  /// DateTime local = TimeZoneUtils.convertDateTimeToIanaTimezone(utc, 'Asia/Ho_Chi_Minh');
  /// // local is 2024-01-01 19:00:00.000
  /// ```
  static DateTime convertDateTimeToIanaTimezone(DateTime dateTime, String iana) {
    try {
      final location = tz.getLocation(iana);
      final utcDateTime = dateTime.isUtc ? dateTime : dateTime.toUtc();
      return tz.TZDateTime.from(utcDateTime, location);
    } catch (_) {
      return dateTime;
    }
  }

  /// Returns a tz.Location from a Timezone object, or UTC if null.
  ///
  /// Example:
  /// ```dart
  /// tz.Location loc = TimeZoneUtils.getLocationFromTimezone(Timezone('Asia/Ho_Chi_Minh', 'ICT', 'GMT+07:00'));
  /// ```
  static tz.Location getLocationFromTimezone(Timezone? timezone) {
    return tz.getLocation(timezone?.name ?? kDefaultTimeZone);
  }

  /// Parses a timezone offset string (e.g. "GMT+07:00", "UTC-04:30", "+07:00", "-04:30") to a Duration.
  static Duration parseOffsetString(String offset) {
    final regex = RegExp(r'([+-])(\d{2}):(\d{2})');
    final match = regex.firstMatch(offset);
    if (match != null) {
      final sign = match.group(1) == '-' ? -1 : 1;
      final hours = int.parse(match.group(2)!);
      final minutes = int.parse(match.group(3)!);
      return Duration(hours: sign * hours, minutes: sign * minutes);
    }
    // fallback: GMT+00:00
    return Duration.zero;
  }

  // --- Formatting ---
  /// Returns a map of all standard representations for a timezone offset.
  ///
  /// Example:
  /// ```dart
  /// var formats = TimeZoneUtils.formatAllTimezoneOffsets(Duration(hours: 7));
  /// // formats['gmt'] == 'GMT+07:00'
  /// // formats['utc'] == 'UTC+07:00'
  /// // formats['iso'] == '+07:00'
  /// ```
  static Map<TimezoneOffsetFormat, String> formatAllTimezoneOffsets(Duration offset) {
    final sign = offset.isNegative ? '-' : '+';
    final hours = offset.inHours.abs().toString().padLeft(2, '0');
    final minutes = (offset.inMinutes.abs() % 60).toString().padLeft(2, '0');
    final iso = '$sign$hours:$minutes';
    final gmt = 'GMT$sign$hours:$minutes';
    final utc = 'UTC$sign$hours:$minutes';
    final z = (offset.inHours == 0 && offset.inMinutes == 0) ? 'Z' : null;
    final map = <TimezoneOffsetFormat, String>{
      TimezoneOffsetFormat.gmt: gmt,
      TimezoneOffsetFormat.utc: utc,
      TimezoneOffsetFormat.iso: iso,
    };
    if (z != null) map[TimezoneOffsetFormat.z] = z;
    return map;
  }

  /// Format a timezone offset as a specific standard.
  ///
  /// [type] can be TimezoneOffsetFormat.gmt, .utc, .iso, or .z (default is .gmt).
  /// Example:
  /// ```dart
  /// TimeZoneUtils.formatTimezoneOffset(Duration(hours: 7), type: TimezoneOffsetFormat.iso); // +07:00
  /// ```
  static String formatTimezoneOffset(Duration offset, {TimezoneOffsetFormat type = TimezoneOffsetFormat.gmt}) {
    final formats = formatAllTimezoneOffsets(offset);
    return formats[type] ?? formats[TimezoneOffsetFormat.gmt]!;
  }

  /// Returns only the default GMT format for the timezone offset of a Timezone object.
  static String formatTimezoneGmtString(Timezone tz) {
    final formats = formatAllTimezoneOffsets(parseOffsetString(tz.offset));
    return formats[TimezoneOffsetFormat.gmt]!;
  }

  /// Format a DateTime in the context of a Timezone (returns 'dd/MM/yyyy' or 'hh:mm')
  ///
  /// Example:
  /// ```dart
  /// DateTime utc = DateTime.utc(2024, 1, 1, 12, 0);
  /// Timezone tz = Timezone('Asia/Ho_Chi_Minh', 'ICT', 'GMT+07:00');
  /// String time = TimeZoneUtils.formatDateTimeWithTimezone(utc, tz, showTime: true);
  /// // time == '19:00'
  /// ```
  static String formatDateTimeWithTimezone(DateTime? dateTime, Timezone? timezone, {bool showTime = false}) {
    if (dateTime == null || timezone == null) return showTime ? kDefaultTimeFormat : kDefaultDateFormat;
    try {
      final location = tz.getLocation(timezone.name);
      final tzDateTime = tz.TZDateTime.from(dateTime.isUtc ? dateTime : dateTime.toUtc(), location);
      if (showTime) {
        // Format as HH:mm
        return '${tzDateTime.hour.toString().padLeft(2, '0')}:${tzDateTime.minute.toString().padLeft(2, '0')}';
      } else {
        // Format as dd/MM/yyyy
        return '${tzDateTime.day.toString().padLeft(2, '0')}/${tzDateTime.month.toString().padLeft(2, '0')}/${tzDateTime.year}';
      }
    } catch (_) {
      return showTime ? kDefaultTimeFormat : kDefaultDateFormat;
    }
  }

  // --- Timezone Selection ---
  /// Returns the default timezone for the user or UTC if not found.
  ///
  /// Example:
  /// ```dart
  /// Timezone tz = TimeZoneUtils.getDefaultTimezone();
  /// ```
  static Timezone getDefaultTimezone({List<Timezone>? tzList, bool defaultToUserTimezone = true}) {
    final list = tzList ?? _uniqueTimezoneList;
    if (!defaultToUserTimezone) {
      return getUtcTimezone(list);
    }
    final String userTimezoneName = DateTime.now().timeZoneName;
    final String userTimezoneOffset = formatTimezoneOffset(DateTime.now().timeZoneOffset);

    return list.firstWhere(
      (tz) => tz.name == userTimezoneName || tz.abbr == userTimezoneName || tz.offset == userTimezoneOffset,
      orElse: () => getUtcTimezone(list),
    );
  }

  /// Returns the UTC timezone from the list.
  ///
  /// Example:
  /// ```dart
  /// Timezone utcTz = TimeZoneUtils.getUtcTimezone(TimeZoneUtils.getUniqueTimezoneList());
  /// ```
  static Timezone getUtcTimezone(List<Timezone> tzList) {
    return tzList.firstWhere(
      (tz) => tz.name == kUtcIana || tz.abbr == kUtcAbbr || tz.offset == kUtcOffset,
      orElse: () => tzList.first,
    );
  }

  /// Get a Timezone by offset string (e.g. 'GMT+07:00') or IANA name, fallback to default if not found.
  ///
  /// Example:
  /// ```dart
  /// Timezone tz = TimeZoneUtils.getTimezoneByOffset('GMT+07:00');
  /// ```
  static Timezone getTimezoneByOffset(String? offsetOrName) {
    final tzList = _uniqueTimezoneList;
    if (offsetOrName == null || offsetOrName.isEmpty) {
      return getDefaultTimezone();
    }
    // Try match by name (IANA)
    final byName = tzList.firstWhereOrNull((tz) => tz.name == offsetOrName);
    if (byName != null) {
      return byName;
    }
    // Try match by offset string (e.g. GMT+07:00)
    final byOffset = tzList.firstWhereOrNull((tz) => tz.offset == offsetOrName);
    if (byOffset != null) {
      return byOffset;
    }
    // Fallback to default
    return getDefaultTimezone();
  }

  /// Returns the best-matching Timezone for a given DateTime (by offset and name).
  ///
  /// Example:
  /// ```dart
  /// Timezone tz = TimeZoneUtils.getTimezoneFromDateTime(DateTime.now());
  /// ```
  static Timezone getTimezoneFromDateTime(DateTime? dateTime) {
    if (dateTime == null) return getDefaultTimezone();
    final offsetString = formatTimezoneOffset(dateTime.timeZoneOffset);
    final name = dateTime.timeZoneName;
    final byName = _uniqueTimezoneList.firstWhereOrNull((tz) => tz.name == name || tz.abbr == name);
    if (byName != null) {
      return byName;
    }
    return getTimezoneByOffset(offsetString);
  }

  /// Returns a Timezone from a DateTime or tz.TZDateTime, preferring IANA name if available.
  ///
  /// Example:
  /// ```dart
  /// Timezone tz = TimeZoneUtils.extractTimezone(DateTime.now());
  /// ```
  static Timezone extractTimezone(DateTime? dateTime) {
    if (dateTime is tz.TZDateTime) {
      final ianaName = dateTime.location.name;
      final offset = formatTimezoneOffset(dateTime.timeZoneOffset);
      return Timezone(ianaName, '', offset);
    } else if (dateTime != null) {
      return getTimezoneFromDateTime(dateTime);
    }
    return getDefaultTimezone();
  }

  /// Lookup Windows timezone ID from IANA timezone name using the combined table.
  static String? lookupWindowsFromIana(String iana) {
    return tz_const.ianaToWindowsTimeZone[iana];
  }

  /// Lookup IANA timezone name from Windows timezone ID using the combined table.
  static String? lookupIanaFromWindows(String windows) {
    return tz_const.windowsToIanaMap[windows];
  }

  /// Helper to lookup Windows timezone by IANA or offset (Syncfusion supported only)
  static String? _lookupSfCalendarWindowsTimeZone(String iana, String offset) {
    for (final row in _sfCalendarTimeZoneTable) {
      if (row['iana'] == iana) return row['windows'];
    }
    for (final row in _sfCalendarTimeZoneTable) {
      if (row['offset'] == offset) return row['windows'];
    }
    return null;
  }
}
