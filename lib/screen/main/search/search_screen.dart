import 'package:auto_route/auto_route.dart';
import 'package:device_calendar/device_calendar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/service/event_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/event/detail_event/detail_event_parameter.dart';
import 'package:family_app/screen/main/search/search_cubit.dart';
import 'package:family_app/screen/main/search/search_state.dart';
import 'package:family_app/utils/extension/color_exten.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/widget/button_line_left_widget.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:family_app/widget/search_custom_filed.dart';
import 'package:flutter/material.dart';

@RoutePage()
class SearchScreen extends BaseBlocProvider<SearchState, SearchCubit> {
  const SearchScreen({super.key});

  @override
  Widget buildPage() => const SearchView();

  @override
  SearchCubit createCubit() => SearchCubit(
        accountService: locator.get(),
        eventService: locator.get<EventService>(),
      );
}

class SearchView extends StatefulWidget {
  const SearchView({super.key});

  @override
  State<SearchView> createState() => _SearchViewState();
}

class _SearchViewState extends BaseBlocPageState<SearchView, SearchState, SearchCubit> {
  @override
  bool get showBack => false;

  @override
  Color? get backgroundAppBarColor => appTheme.background;

  @override
  Widget buildAppBar(BuildContext context, SearchCubit cubit, SearchState state) {
    return Padding(
      padding: padding(vertical: 12, horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: SearchCustomField(
              hintText: LocaleKeys.search_text.tr(),
              onGetSearchValue: (value) {
                cubit.search(value);
              },
              getSearchStatus: (isSearching) {},
              backgroundColor: appTheme.whiteText,
              radius: 8,
              textStyle: AppStyle.regular16(),
              paddingTextfield: EdgeInsets.zero,
              prefixIcon: IconButton(
                onPressed: null,
                icon: ImageAssetCustom(imagePath: Assets.images.searchBorder.path, size: 24.w),
              ),
            ),
          ),
          SizedBox(width: 7.w),
          GestureDetector(
            onTap: context.maybePop,
            child: Text(LocaleKeys.cancel_text.tr(), style: AppStyle.regular14(color: appTheme.primaryColor)),
          ),
        ],
      ),
    );
  }

  @override
  Widget buildBody(BuildContext context, SearchCubit cubit, SearchState state) {
    return ColoredBox(
      color: appTheme.whiteText,
      child: Padding(
        padding: padding(top: 6, horizontal: 16),
        child: state.models.isNotEmpty
            ? ListView(
                children: (state.groupedEvents?.entries ?? []).map((entry) {
                  final date = entry.key;
                  final events = entry.value;
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(date, style: AppStyle.regular14(color: appTheme.labelColor)),
                      SizedBox(height: 11.h),
                      ...events.map((event) {
                        return Padding(
                          padding: padding(bottom: 8),
                          child: InkWell(
                            onTap: () => context.pushRoute(
                              DetailEventRoute(
                                parameter: DetailEventParameter(eventModels: event),
                              ),
                            ),
                            child: buttonLineLeftWidget(
                              context,
                              title: event.name ?? '',
                              text: event.description ?? '',
                              timeTitle: event.fromDate?.HH_mm ?? '',
                              timeText: event.toDate?.HH_mm ?? '',
                              color: event.color.toColor.withOpacity(0.3),
                              colorLine: event.color.toColor,
                            ),
                          ),
                        );
                      }).toList(),
                    ],
                  );
                }).toList(),
              )
            : Container(
                padding: padding(top: 39),
                width: double.infinity,
                alignment: Alignment.topCenter,
                child: Text(
                  LocaleKeys.no_result_text.tr(),
                  style: AppStyle.regular14(color: appTheme.fadeTextColor),
                ),
              ),
      ),
    );
  }
}
