import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/constant/app_constant.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/event_service.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/screen/main/search/search_state.dart';

class SearchCubit extends BaseCubit<SearchState> {
  final EventService eventService;
  final AccountService accountService;

  SearchCubit({
    required this.accountService,
    required this.eventService,
  }) : super(SearchState());

  void updateQuery(String query) {
    emit(state.copyWith(query: query, isSearching: query.isNotEmpty));
  }

  Future<void> search(String query) async {
    try {
      final result = await eventService.getEventsByFamilyId(
        accountService.familyId,
      );
      final filtered = query.trim().isEmpty
          ? result
          : result.where((e) =>
              (e.name ?? '').toLowerCase().contains(query.trim().toLowerCase()) ||
              (e.description ?? '').toLowerCase().contains(query.trim().toLowerCase())
            ).toList();
      groupEventsByDate(filtered);
      emit(state.copyWith(models: filtered));
    } catch (e) {
      print(e);
    }
  }

  void groupEventsByDate(List<EventModels> events) {
    final grouped = <String, List<EventModels>>{};
    for (final event in events) {
      final dateKey = DateFormat(FULL_DAY_FORMAT).format(DateTime.parse(event.createdAt ?? ''));
      grouped.putIfAbsent(dateKey, () => []).add(event);
    }
    emit(state.copyWith(groupedEvents: grouped));
  }
}
