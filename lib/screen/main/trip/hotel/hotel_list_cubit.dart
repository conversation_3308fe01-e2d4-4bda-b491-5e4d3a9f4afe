import 'dart:math';

import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/data/model/amadeus/hotel_model.dart';
import 'package:family_app/data/model/hotel_model.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/data/repository/amadeus/iamadeus_repository.dart';
import 'package:family_app/data/usecase/hotel_booking_usecase.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/trip/hotel/hotel_list_parameter.dart';
import 'package:family_app/screen/main/trip/hotel/hotel_list_state.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';

import 'details/hotel_detail_parameter.dart';

class HotelListCubit extends BaseCubit<HotelListState> {
  final _tag = "HotelListCubit";
  final IActivityRepository activityRepository;
  final HotelBookingUseCase useCase;
  final HotelListParameter parameter;

  String? keyword;

  // bool isBooking = false;
  // int pageItem = 10;
  // int maxPage = 10;
  int currentPage = 0;

  // List<AmadeusHotelModel> hotelList = [];

  HotelListCubit({
    required this.activityRepository,
    required this.useCase,
    required this.parameter,
  }) : super(HotelListState());

  @override
  void onInit() {
    _init();
    super.onInit();
  }

  _init() async {
    try {
      emit(state.copyWith(
          isLoading: true,
          city: parameter.activity.city,
          country: parameter.activity.country));
      List<int>? ratings;
      if (parameter.activity.hotelPreferences?.starRating != null) {
        ratings = [parameter.activity.hotelPreferences!.starRating!];
      }

      List<HotelModel> hotelList = await useCase.searchHotels(
          parameter.activity.cityCode ?? '',
          ratings: ratings,
          amenities: parameter.activity.hotelPreferences?.amenities);
      hotelList = hotelList.map((e) {
        e.city = parameter.activity.city;
        e.country = parameter.activity.country;
        return e;
      }).toList();
      emit(state.copyWith(hotels: hotelList, isLoading: false));
      logd("message length ${hotelList.length}");
    } catch (e) {
      logd("_init $e", tag: _tag);
    }
  }

  // _getHotelPage(int i) async {
  //   if(kDebugMode && (state.hotels?.length ?? 0) > 50){
  //     return;
  //   }
  //   try {
  //     logd("fetch page $i / $maxPage", tag: _tag);
  //     currentPage = i;
  //     int startIndex = i * pageItem;
  //     if(startIndex >= hotelList.length) {
  //       emit(state.copyWith(isLoading: false));
  //       return;
  //     }
  //     int endIndex = min(startIndex + pageItem, hotelList.length);
  //     var hotels = hotelList.sublist(startIndex, endIndex);
  //     var hotelIds = hotels.map((e) => e.hotelId).toSet().join(",");
  //     var hotelsOffers = await amadeusRepository.getHotelOffers(
  //         "[$hotelIds]", parameter.activity.hotelPreferences?.numberOfGuests ?? 1);
  //
  //     for (var offer in hotelsOffers) {
  //       if (offer.hotel != null && offer.offers.isNotEmpty) {
  //         AmadeusHotelModel hotel = offer.hotel!;
  //         hotel.city = parameter.activity.city;
  //         hotel.country = parameter.activity.country;
  //         hotel.offers = offer.offers;
  //         List<AmadeusHotelModel> currentHotelList = [...state.hotels ?? []];
  //         currentHotelList.add(hotel);
  //         emit(state.copyWith(hotels: currentHotelList, isLoading: currentHotelList.isEmpty));
  //       }
  //     }
  //     if(isClose || isBooking) return;
  //
  //     if (currentPage < maxPage - 1) {
  //       currentPage++;
  //       _getHotelPage(currentPage);
  //     } else {
  //       logd("fetch page done", tag: _tag);
  //       emit(state.copyWith(isLoading: false));
  //     }
  //   } catch (e) {
  //     logd("_getHotelPage $e", tag: _tag);
  //   }
  // }

  onGetSearchValue(String text) {
    keyword = text;
    if (keyword != null && keyword!.isNotEmpty) {
      emit(state.copyWith(isLoading: true));
      List<AmadeusHotelModel> tmpHotelList = [];
      for (var hotel in state.hotels ?? []) {
        if (hotel.name?.toLowerCase().contains(keyword!.toLowerCase()) ??
            false) {
          tmpHotelList.add(hotel);
        }
      }
      emit(state.copyWith(searchHotels: tmpHotelList, isLoading: false));
    } else {
      emit(state.copyWith(searchHotels: [], isLoading: false));
    }
  }

  Future<void> onTap2Hotel(BuildContext context, HotelModel hotel) async {
    // isBooking = true;
    await context.pushRoute(HotelDetailRoute(
        parameter: HotelDetailParameter(
      hotelId: hotel.id ?? "",
      hotel: hotel,
      activity: parameter.activity,
      dayIndex: parameter.dayIndex,
      isSavedHotel: false,
    )));
    // _getHotelPage(currentPage + 1 );
    // isBooking = false;
  }
}
