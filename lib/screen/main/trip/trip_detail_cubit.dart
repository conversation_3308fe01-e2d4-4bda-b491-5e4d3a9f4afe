import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/hotel_booking_model.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/data/repository/activity/iactivity_repository.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/trip/hotel/details/hotel_detail_parameter.dart';
import 'package:family_app/screen/main/trip/trip_detail_parameter.dart';
import 'package:family_app/utils/bottom_sheet.dart';
import 'package:family_app/utils/content_provider.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/src/widgets/framework.dart';

import 'trip_detail_state.dart';
import 'trip_select_activity_type_parameter.dart';
import 'trip_select_activity_type_screen.dart';

class TripDetailCubit extends BaseCubit<TripDetailState> {
  final IActivityRepository activityRepository;
  final TripDetailParameter parameter;

  TripDetailCubit({required this.activityRepository, required this.parameter})
      : super(TripDetailState(
            activityId: parameter.activityId, activity: parameter.activity));

  @override
  void onInit() {
    super.onInit();
    locator.registerSingleton(this);
    _fetchTripDetails();
  }

  @override
  Future<void> close() {
    locator.unregister<TripDetailCubit>();
    return super.close();
  }

  /// Sets the selected day index for the trip.
  void setSelectedIndex(int index) {
    if (index != state.selectedIndex) {
      emit(state.copyWith(selectedIndex: index));
    }
  }

  /// Fetches trip details and updates state.
  Future<void> _fetchTripDetails() async {
    if (state.loading) return;
    emit(state.copyWith(loading: true));
    try {
      final activity = await activityRepository
          .getActivityById(parameter.activity?.uuid ?? parameter.activityId);
      await _processActivityData(activity);
    } catch (e) {
      AppLogger.e('Error fetching trip details: $e');
      emit(state.copyWith(loading: false));
    }
  }

  /// Processes activity data and updates state accordingly.
  Future<void> _processActivityData(ActivityModel activity) async {
    try {
      final days = _generateDaysList(activity);
      final updatedActivity = await _fetchTripImagesIfNeeded(activity);
      logd("Trip Hotel info: ${updatedActivity.hotelBookings}");

      //Log itinerary details
      if (updatedActivity.itinerary == null ||
          updatedActivity.itinerary!.isEmpty) {
        logd("Trip Itinerary is empty or null");
      } else {
        logd(
            "Trip Itinerary: ${updatedActivity.itinerary!.map((e) => e.toString()).join(', ')}");
      }
      emit(state.copyWith(
        loading: false,
        activity: updatedActivity,
        heroImageUrl: updatedActivity.imagePath,
        days: days,
      ));
    } catch (e) {
      AppLogger.e('Error processing activity data: $e');
      emit(state.copyWith(loading: false));
    }
  }

  /// Generates a list of day labels from the itinerary.
  List<String> _generateDaysList(ActivityModel activity) {
    return activity.itinerary
            ?.asMap()
            .entries
            .map((entry) => 'Day ${entry.key + 1}')
            .toList() ??
        [];
  }

  /// Fetches trip images if not already present, returns a new ActivityModel.
  Future<ActivityModel> _fetchTripImagesIfNeeded(ActivityModel activity) async {
    if (activity.imagePath != null) return activity;
    String? imagePath;
    try {
      if (activity.city != null) {
        imagePath = await provider.fetchImageUrl(activity.city!);
      } else if (activity.country != null) {
        imagePath = await provider.fetchImageUrl(activity.country!);
      }
    } catch (e) {
      AppLogger.e('Error fetching trip images: $e');
    }
    return activity.copyWith(imagePath: imagePath);
  }

  /// Fetches trip detail and updates state.
  Future<void> fetchTripDetail() async {
    if (state.loading) return;
    emit(state.copyWith(loading: true));
    try {
      final result = await activityRepository.getActivityById(state.activityId);
      final updatedResult = await _fetchTripImagesIfNeeded(result);
      emit(state.copyWith(
        loading: false,
        activity: updatedResult,
        heroImageUrl: updatedResult.imagePath,
        days: _generateDaysList(updatedResult),
      ));
    } catch (e) {
      AppLogger.e("Error fetching trip: $e");
      emit(state.copyWith(loading: false));
    }
  }

  /// Returns the timeline list for a given day index.
  List<ActivityTimelineItem> getTimelineList(
      ActivityModel activityModel, int dayIndex) {
    if (dayIndex < 0 || dayIndex >= (activityModel.itinerary?.length ?? 0)) {
      return [];
    }
    final List<ActivityTimelineItem> timelineItems = [];
    final itinerary = activityModel.itinerary![dayIndex];
    final dateTime =
        activityModel.fromDate?.toLocalDT.add(Duration(days: dayIndex));
    if (dateTime == null) return [];
    _addActivitiesToTimeline(timelineItems, itinerary, dateTime);
    _addTransfersToTimeline(timelineItems, itinerary);
    _addHotelBookingsToTimeline(timelineItems, activityModel, dateTime);
    return timelineItems;
  }

  void _addActivitiesToTimeline(List<ActivityTimelineItem> timelineItems,
      Itinerary itinerary, DateTime dateTime) {
    itinerary.activities?.forEach((activity) {
      DateTime timelineDateTime = _parseActivityTime(activity.time, dateTime);
      timelineItems.add(
          ActivityTimelineItem(dateTime: timelineDateTime, data: activity));
    });
  }

  DateTime _parseActivityTime(String time, DateTime dateTime) {
    if (time.isEmpty || time == 'AM') {
      return DateTime(dateTime.year, dateTime.month, dateTime.day, 10, 0);
    } else if (time == 'PM') {
      return DateTime(dateTime.year, dateTime.month, dateTime.day, 16, 0);
    } else if (time.contains(' - ')) {
      final times = time.split(' - ');
      if (times.length == 2) {
        final startTime = times[0].trim().split(':');
        if (startTime.length == 2) {
          final hour = int.tryParse(startTime[0]) ?? 10;
          final minute = int.tryParse(startTime[1]) ?? 0;
          return DateTime(
              dateTime.year, dateTime.month, dateTime.day, hour, minute);
        }
      }
      return DateTime(dateTime.year, dateTime.month, dateTime.day, 10, 0);
    }
    return DateTime(dateTime.year, dateTime.month, dateTime.day, 10, 0);
  }

  void _addTransfersToTimeline(
      List<ActivityTimelineItem> timelineItems, Itinerary itinerary) {
    itinerary.transfers?.forEach((transfer) {
      if (transfer.fromTime != null) {
        timelineItems.add(ActivityTimelineItem(
            dateTime: transfer.fromTime!.toLocalDT, data: transfer));
      }
    });
  }

  void _addHotelBookingsToTimeline(List<ActivityTimelineItem> timelineItems,
      ActivityModel activityModel, DateTime dateTime) {
    activityModel.hotelBookings?.forEach((booking) {
      if (booking.checkInDate != null && booking.checkOutDate != null) {
        var checkInMilis =
            booking.checkInDate!.toLocalDT.millisecondsSinceEpoch;
        var checkOutMilis =
            booking.checkOutDate!.toLocalDT.millisecondsSinceEpoch;
        var dayTripMilis = dateTime.millisecondsSinceEpoch;
        if (dayTripMilis >= checkInMilis && dayTripMilis < checkOutMilis) {
          timelineItems.add(ActivityTimelineItem(
              dateTime: booking.checkInDate!.toLocalDT, data: booking));
        }
      }
    });
  }

  /// Handles the add new item action from the UI.
  void onAddNewItemPressed(BuildContext context, int dayIndex) {
    if (state.activity == null) return;
    BottomSheetUtils.showHeightReturnBool(context,
            height: 0.3,
            child: TripSelectActivityTypeBts(
                parameter:
                    TripSelectActivityTypeParameter(state.activity!, dayIndex)))
        .then((value) {
      AppLogger.d('Add new item pressed return value: $value');
      if (value == true) {
        _fetchTripDetails();
      }
    });
  }

  /// Navigates to the hotel booking detail page.
  Future<void> onBookHotel(
      BuildContext context, HotelBookingModel hotel) async {
    if (parameter.activity == null) return;
    await context.pushRoute(HotelDetailRoute(
        parameter: HotelDetailParameter(
      hotelId: hotel.hotelId ?? '',
      activity: parameter.activity!,
      location: hotel.location,
      dayIndex: state.selectedIndex,
      isSavedHotel: true,
    )));
  }

  /// Toggles edit mode for the trip detail screen.
  void toggleEditMode() {
    emit(state.copyWith(editMode: !state.editMode));
  }

  /// Updates the trip name and dates, saving to the repository.
  Future<void> updateTripNameAndDates(
      String name, DateTime? fromDate, DateTime? toDate) async {
    if (state.activity == null) return;
    emit(state.copyWith(isSaving: true));
    try {
      final updatedActivity = state.activity!.copyWith(
        name: name,
        fromDate: fromDate?.toIso8601String(),
        toDate: toDate?.toIso8601String(),
      );
      await activityRepository.updateActivity(
          updatedActivity.uuid, updatedActivity.toCreateActivityParameter());
      emit(state.copyWith(
        activity: updatedActivity,
        isSaving: false,
        saveTripSuccess: true,
      ));
    } catch (e) {
      AppLogger.e('Error updating trip name and dates: $e');
      emit(state.copyWith(isSaving: false, saveTripSuccess: false));
    }
  }

  /// Updates the trip title only in the state.
  void updateTripTitle(String name) {
    if (state.activity == null) return;
    final updatedActivity = state.activity!.copyWith(name: name);
    emit(state.copyWith(activity: updatedActivity));
  }

  /// Updates the trip dates only in the state.
  void updateTripDates(DateTime fromDate, DateTime toDate) {
    if (state.activity == null) return;
    final updatedActivity = state.activity!.copyWith(
      fromDate: fromDate.toIso8601String(),
      toDate: toDate.toIso8601String(),
    );
    emit(state.copyWith(activity: updatedActivity));
  }
}
