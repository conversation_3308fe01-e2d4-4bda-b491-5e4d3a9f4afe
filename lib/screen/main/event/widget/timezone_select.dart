import 'package:flutter/material.dart';
import 'package:family_app/data/model/timezone.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/main.dart';
import 'custom_dropdown_builder.dart';
import 'package:family_app/utils/timezone.dart';

class TimezoneSelect extends StatelessWidget {
  final Timezone? timezone;
  final ValueChanged<Timezone> onChanged;
  final TextStyle? titleStyle;
  final bool defaultToUserTimezone;

  const TimezoneSelect({
    Key? key,
    required this.timezone,
    required this.onChanged,
    this.titleStyle,
    this.defaultToUserTimezone = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final tzList = TimeZoneUtils.getUniqueTimezoneList();
    final defaultTimezone = TimeZoneUtils.getDefaultTimezone(defaultToUserTimezone: defaultToUserTimezone);
    return Container(
      padding: EdgeInsets.zero,
      child: Row(
        children: [
          Expanded(
            child: Text(
              LocaleKeys.timezone.tr(),
              style: titleStyle ??
                  TextStyle(
                    fontSize: 16,
                    color: appTheme.blackText,
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ),
          CustomDropdownBuilder<Timezone>(
            data: tzList,
            value: timezone ?? defaultTimezone,
            onChanged: onChanged,
            textBuilder: TimeZoneUtils.formatTimezoneGmtString,
          ),
        ],
      ),
    );
  }
}
