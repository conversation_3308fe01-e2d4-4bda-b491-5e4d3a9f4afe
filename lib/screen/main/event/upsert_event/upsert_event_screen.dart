import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/constant/timezone.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/service/calendar_service.dart';
import 'package:family_app/config/service/event_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/data/model/device_event.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/data/model/repeat_config_model.dart';
import 'package:family_app/data/model/timezone.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/screen/main/event/upsert_event/upsert_event_parameter.dart';
import 'package:family_app/utils/extension/date_time_ext.dart';
import 'package:family_app/widget/appbar_custom.dart';
import 'package:family_app/widget/bottom_sheet/date_picker_bottom_sheet.dart';
import 'package:family_app/widget/bottom_sheet/select_member_bts.dart';
import 'package:family_app/widget/circle_item.dart';
import 'package:family_app/widget/list_color_selection.dart';
import 'package:family_app/widget/persons_view.dart';
import 'package:family_app/widget/switch.dart';
import 'package:family_app/widget/textfield/row_text_field_view.dart';
import 'package:family_app/screen/main/event/widget/timezone_select.dart';
import 'package:family_app/screen/main/event/widget/custom_dropdown_builder.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:family_app/utils/timezone.dart';

import 'upsert_event_cubit.dart';
import 'upsert_event_state.dart';

@RoutePage()
class UpsertEventPage extends BaseBlocProvider<UpsertEventState, UpsertEventCubit> {
  const UpsertEventPage({
    super.key,
    required this.upsertEventParameter,
  });

  final UpsertEventParameter upsertEventParameter;

  @override
  Widget buildPage() => UpsertEventView(parameter: upsertEventParameter);

  @override
  UpsertEventCubit createCubit() => UpsertEventCubit(
        eventUsecase: locator.get(),
        parameter: upsertEventParameter,
        eventRepository: locator.get(),
        accountService: locator.get(),
        activityRepository: locator.get(),
        eventService: locator.get<EventService>(),
      );
}

class UpsertEventView extends StatefulWidget {
  const UpsertEventView({super.key, required this.parameter});

  final UpsertEventParameter parameter;

  @override
  State<UpsertEventView> createState() => _UpsertEventViewState();
}

class _UpsertEventViewState extends BaseBlocPageState<UpsertEventView, UpsertEventState, UpsertEventCubit> {
  @override
  String get title {
    final eventName = widget.parameter.model?.name?.trim();
    return (eventName != null && eventName.isNotEmpty) ? LocaleKeys.edit_event.tr() : LocaleKeys.new_event.tr();
  }

  TextStyle get titleStyle => AppStyle.regular16V2(color: appTheme.blackText);

  @override
  bool listenWhen(UpsertEventState previous, UpsertEventState current) {
    if (previous.status != current.status) {
      if (current.status == UpsertEventStatus.success) {
        context.maybePop();
      }
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildAppBar(BuildContext context, UpsertEventCubit cubit, UpsertEventState state) {
    return CustomAppBar2(
      title: title,
      showBack: true,
      actions: widget.parameter.model is DeviceEventModel
          ? []
          : [
              widget.parameter.model == null
                  ? CircleItem(
                      onTap: cubit.createEvent,
                      backgroundColor: appTheme.blackColor.withValues(alpha: .05),
                      padding: padding(all: 7),
                      child: const Icon(Icons.check),
                    )
                  : PopupMenuButton<String>(
                      icon: Container(
                        padding: padding(all: 2),
                        child: Assets.icons.buttonThreeDot.svg(width: 32.w, height: 32.w),
                      ),
                      color: appTheme.whiteText,
                      onOpened: () => cubit.emit(state.copyWith(isShowMenu: true)),
                      onCanceled: () => cubit.emit(state.copyWith(isShowMenu: false)),
                      onSelected: (String value) => cubit.onMore(context, value),
                      padding: EdgeInsets.zero,
                      itemBuilder: (ctx) => <PopupMenuEntry<String>>[
                        PopupMenuItem<String>(
                          value: 'edit',
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(Icons.check),
                              const SizedBox(width: 8),
                              Text(
                                LocaleKeys.edit_event.tr(),
                                style: AppStyle.regular14(color: appTheme.blackText),
                              ),
                            ],
                          ),
                        ),
                        PopupMenuItem<String>(
                          value: 'delete',
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Assets.icons.icDelete.svg(
                                width: 24,
                                height: 24,
                                colorFilter: ColorFilter.mode(appTheme.red3CColor, BlendMode.srcIn),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                LocaleKeys.delete_event_text.tr(),
                                style: AppStyle.regular14(color: appTheme.red3CColor),
                              ),
                            ],
                          ),
                        ),
                      ],
                    )
            ],
    );
  }

  // @override
  // List<Widget>? appBarActions(UpsertEventCubit cubit, UpsertEventState state) {
  //   return [
  //     GestureDetector(
  //       onTap: cubit.createEvent,
  //       child: RoundItemView(
  //         text: LocaleKeys.save.tr(),
  //         textColor: appTheme.primaryColor,
  //       ),
  //     )
  //   ];
  // }

  @override
  Widget buildBody(BuildContext context, UpsertEventCubit cubit, UpsertEventState state) {
    return Container(
      margin: const EdgeInsets.only(top: 10, left: 8, right: 8, bottom: 10),
      padding: padding(all: 8),
      decoration: BoxDecoration(
        color: appTheme.backgroundWhite,
        borderRadius: const BorderRadius.all(Radius.circular(20)),
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.all(Radius.circular(20)),
        child: ListView(
          padding: padding(bottom: 10),
          children: state.isDeviceEvent ? _buildDeviceEvent(context, cubit, state) : _buildFLEvent(context, cubit, state),
        ),
      ),
    );
  }

  List<Widget> _buildDeviceEvent(BuildContext context, UpsertEventCubit cubit, UpsertEventState state) {
    return [
      RowTextFieldView(
        handler: cubit.title,
        bottomSizedBox: 0,
        formHandler: cubit.formHandler,
        textStyle: AppStyle.bold20(color: appTheme.blackText),
        hintStyle: AppStyle.bold20(color: appTheme.hintColor),
      ),
      RowTextFieldView(
        handler: cubit.description,
        topPadding: 17,
        bottomSizedBox: 28,
        formHandler: cubit.formHandler,
        maxLine: 5,
        textStyle: AppStyle.normal16(color: appTheme.blackText),
        hintStyle: AppStyle.normal16(color: appTheme.hintColor),
      ),
      _buildDivider(),
      _buildCalendar(context, cubit, state),
      _buildDivider(),
      _buildEventTime(context, cubit, state),
      const SizedBox(height: 12),
      _buildReminder(context, cubit, state),
      _buildDivider(),
    ];
  }

  List<Widget> _buildFLEvent(BuildContext context, UpsertEventCubit cubit, UpsertEventState state) {
    return [
      RowTextFieldView(
        handler: cubit.title,
        bottomSizedBox: 0,
        formHandler: cubit.formHandler,
        textStyle: AppStyle.bold20(color: appTheme.blackText),
        hintStyle: AppStyle.bold20(color: appTheme.hintColor),
      ),
      RowTextFieldView(
        handler: cubit.description,
        topPadding: 17,
        bottomSizedBox: 28,
        formHandler: cubit.formHandler,
        maxLine: 5,
        textStyle: AppStyle.normal16(color: appTheme.blackText),
        hintStyle: AppStyle.normal16(color: appTheme.hintColor),
      ),
      ColoredBox(
        color: appTheme.whiteText,
        child: Padding(
          padding: padding(horizontal: 8, bottom: 20),
          child: ListColorSelection(
            selectedColor: state.selectedColor,
            onSelectColor: cubit.updateSelectedColor,
          ),
        ),
      ),
      _buildDivider(),
      _buildEventTime(context, cubit, state),
      _buildDivider(),
      _buildAssign(context, cubit, state),
      _buildDivider(),
      Text(
        '${LocaleKeys.viewAndEditTodoMsg.tr()}:',
        style: AppStyle.regular12(color: appTheme.grayV2),
      ),
      _buildDivider(),
      _buildActivity(context, cubit, state),
      _buildDivider(),
      _buildRepeat(context, cubit, state),
      _buildDivider(),
      _buildReminder(context, cubit, state),
      _buildDivider(),
      _buildDivider(),
    ];
  }

  Widget _buildDivider() => const SizedBox(height: 12);

  Widget _buildEventTimeDivider() => const SizedBox(height: 8);

  Widget _buildEventTime(BuildContext context, UpsertEventCubit cubit, UpsertEventState state) {
    return Container(
        padding: const EdgeInsets.only(top: 8, bottom: 12, left: 12, right: 12),
        decoration: BoxDecoration(
          color: appTheme.whiteText,
          borderRadius: const BorderRadius.all(Radius.circular(20)),
          border: Border.all(
            color: appTheme.borderColorV2,
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                    child: Text(
                  LocaleKeys.all_day.tr(),
                  style: titleStyle,
                )),
                if (state.isDeviceEvent)
                  Text(
                    state.isAllDay ? LocaleKeys.on.tr() : LocaleKeys.off.tr(),
                    style: titleStyle,
                  )
                else
                  SwitchCustom(state.isAllDay, cubit.handelEventAllDay)
              ],
            ),
            _buildEventTimeDivider(),
            _buildSelectTime(
              context,
              title: LocaleKeys.start.tr(),
              dateTime: state.startDate,
              state: state,
              disable: state.isDeviceEvent,
              onSelected: cubit.updateStartDate,
            ),
            _buildEventTimeDivider(),
            _buildSelectTime(
              context,
              title: LocaleKeys.end.tr(),
              dateTime: state.endDate,
              disable: state.isDeviceEvent,
              state: state,
              onSelected: cubit.updateEndDate,
            ),
            if (!state.isAllDay) ...[
              _buildDivider(),
              TimezoneSelect(
                timezone: state.timezone,
                onChanged: cubit.onChangeTimezone,
                titleStyle: titleStyle,
              ),
            ]
          ],
        ));
  }

  _buildSelectTime(
    BuildContext context, {
    required String title,
    DateTime? dateTime,
    bool disable = true,
    required UpsertEventState state,
    required ValueChanged<DateTime> onSelected,
  }) {
    final timezone = state.timezone;
    return Row(
      children: [
        Expanded(flex: 1, child: Text(title, style: titleStyle)),
        const SizedBox(width: 8),
        Expanded(
          flex: 4,
          child: Container(
            padding: padding(horizontal: 8, vertical: 8),
            decoration: BoxDecoration(
              color: appTheme.whiteText,
              borderRadius: const BorderRadius.all(Radius.circular(4)),
              border: Border.all(
                color: appTheme.borderColorV2,
                width: 1,
              ),
            ),
            child: InkWell(
              onTap: disable ? null : () => DatePickerBottomSheet.show(context, title: title, onSelected: onSelected, initialDate: dateTime, use24HourFormat: true, timezone: timezone, hideTime: state.isAllDay),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      TimeZoneUtils.formatDateTimeWithTimezone(dateTime, timezone, showTime: false),
                      style: AppStyle.regular14V2(),
                    ),
                  ),
                  SvgPicture.asset(
                    Assets.icons.icCalendar32.path,
                    width: 24.w,
                    height: 24.h,
                    colorFilter: ColorFilter.mode(appTheme.grayV2, BlendMode.srcATop),
                  ),
                ],
              ),
            ),
          ),
        ),
        if (!state.isAllDay) ...[
          const SizedBox(width: 8),
          Expanded(
            flex: 2,
            child: Container(
              padding: padding(horizontal: 8, vertical: 8),
              decoration: BoxDecoration(
                color: appTheme.whiteText,
                borderRadius: const BorderRadius.all(Radius.circular(4)),
                border: Border.all(
                  color: appTheme.borderColorV2,
                  width: 1,
                ),
              ),
              child: InkWell(
                onTap: disable ? null : () => DatePickerBottomSheet.show(context, title: title, onSelected: onSelected, initialDate: dateTime, use24HourFormat: true, timezone: timezone, hideTime: state.isAllDay),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        TimeZoneUtils.formatDateTimeWithTimezone(dateTime, timezone, showTime: true),
                        style: AppStyle.regular14V2(),
                      ),
                    ),
                    SvgPicture.asset(
                      Assets.icons.icClock32.path,
                      width: 24.w,
                      height: 24.h,
                      colorFilter: ColorFilter.mode(appTheme.grayV2, BlendMode.srcATop),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildAssign(BuildContext context, UpsertEventCubit cubit, UpsertEventState state) {
    return InkWell(
      onTap: () => SelectMemberBts.show(
        context,
        selectedMembers: state.memberList.map((e) => e.familyMemberUuid ?? '').toList(),
        onSelected: cubit.updateSelectedMember,
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              LocaleKeys.visibleTo.tr(),
              style: titleStyle,
            ),
          ),
          PersonsView(accounts: state.memberList),
          Assets.images.arrowRight.image(width: 20, height: 20),
        ],
      ),
    );
  }

  Widget _buildActivity(BuildContext context, UpsertEventCubit cubit, UpsertEventState state) {
    return Container(
      color: appTheme.whiteText,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocaleKeys.linkToActivity.tr(),
            style: titleStyle,
          ),
          Container(
            width: double.infinity,
            margin: padding(top: 8),
            padding: padding(horizontal: 12, vertical: 16),
            decoration: BoxDecoration(
              color: appTheme.whiteText,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: appTheme.hintColor),
            ),
            child: DropdownSearch<ActivityModel>(
              mode: Mode.custom,
              items: (filter, props) => cubit.getAllActivities(name: filter),
              selectedItem: state.activity,
              compareFn: (c1, c2) => c1.uuid == c2.uuid,
              popupProps: PopupProps.menu(
                showSelectedItems: true,
                showSearchBox: true,
                searchFieldProps: TextFieldProps(
                  cursorColor: appTheme.primaryColor,
                  decoration: InputDecoration(
                    filled: true,
                    fillColor: appTheme.transparentWhiteColor,
                    hintText: LocaleKeys.searchByActivity.tr(),
                    hintStyle: AppStyle.regular14(color: appTheme.hintColor),
                    prefixIcon: Container(width: 20, height: 20, alignment: Alignment.center, child: Assets.icons.icSearch.svg(width: 20, height: 20)),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                  ),
                ),
                itemBuilder: (_, item, isDisabled, isSelected) {
                  return Container(
                    color: isSelected ? appTheme.dropdownSelectedColor : appTheme.whiteText,
                    padding: padding(top: 15, bottom: 14, horizontal: 16),
                    child: Row(
                      children: [Expanded(child: Text(item.name ?? '', style: titleStyle)), const SizedBox(width: 12), if (isSelected) Icon(Icons.check, color: appTheme.primaryColorV2)],
                    ),
                  );
                },
              ),
              onChanged: (value) => {if (value != null) cubit.updateSelectedActivity(value)},
              itemAsString: (item) => item.name ?? '',
              dropdownBuilder: (context, selectedItem) {
                return Text(
                  selectedItem?.name ?? LocaleKeys.selectAnActivity.tr(),
                  style: AppStyle.regular14(color: selectedItem == null ? appTheme.hintColor : appTheme.blackColor),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  _buildCalendar(BuildContext context, UpsertEventCubit cubit, UpsertEventState state) {
    var event = (widget.parameter.model as DeviceEventModel).event;
    CalendarService calendarService = locator.get<CalendarService>();
    var calendar = calendarService.getCalendar(event.calendarId ?? "");

    return Row(
      children: [
        Text(
          LocaleKeys.calendar.tr(),
          style: titleStyle,
        ),
        const SizedBox(width: 4),
        Expanded(
          child: Text(
            calendar?.name ?? "",
            textAlign: TextAlign.right,
            overflow: TextOverflow.ellipsis,
            style: titleStyle,
          ),
        ),
        const SizedBox(width: 4),
        Platform.isIOS ? Assets.images.appleCalendar.image(height: 24, width: 24) : Assets.images.googleCalendar.svg(height: 24, width: 24)
      ],
    );
  }

  _buildReminder(BuildContext context, UpsertEventCubit cubit, UpsertEventState state) {
    return Container(
      padding: EdgeInsets.zero,
      child: Row(
        children: [
          Expanded(
            child: Text(
              LocaleKeys.reminder.tr(),
              style: titleStyle,
            ),
          ),
          if (state.isDeviceEvent)
            Text(getReminderText(state.reminder ?? 0), style: titleStyle)
          else
            CustomDropdownBuilder<int>(
              data: setupReminderMinutes,
              value: state.reminder ?? setupReminderMinutes.first,
              onChanged: cubit.onChangeReminder,
              textBuilder: (e) => getReminderText(e),
            ),
        ],
      ),
    );
  }

  String getReminderText(int e) {
    if (e == 0) {
      return LocaleKeys.none.tr();
    } else if (e <= 60) {
      return "$e ${LocaleKeys.minutes.tr()}";
    } else {
      return "${e ~/ 60} ${LocaleKeys.hours.tr()}";
    }
  }

  _buildRepeat(BuildContext context, UpsertEventCubit cubit, UpsertEventState state) {
    return Container(
        padding: EdgeInsets.zero,
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    LocaleKeys.repeat_this_event.tr(),
                    style: titleStyle,
                  ),
                ),
                CustomDropdownBuilder<EventRepeatType>(
                  data: EventRepeatType.values,
                  value: state.repeatType,
                  onChanged: cubit.onChangeEventRepeatType,
                  textBuilder: (e) => e.displayName,
                ),
              ],
            ),
            if (state.repeatType == EventRepeatType.custom) _buildDayOfWeek(context, cubit, state)
          ],
        ));
  }

  Widget _buildDayOfWeek(BuildContext context, UpsertEventCubit cubit, UpsertEventState state) {
    List<WeekDay> daysOfWeek = [WeekDay.monday, WeekDay.tuesday, WeekDay.wednesday, WeekDay.thursday, WeekDay.friday, WeekDay.saturday, WeekDay.sunday];
    if (!state.weekStartsOnMonday) {
      daysOfWeek = [WeekDay.sunday, WeekDay.monday, WeekDay.tuesday, WeekDay.wednesday, WeekDay.thursday, WeekDay.friday, WeekDay.saturday];
    }
    return Center(
      child: Container(
        padding: padding(vertical: 8),
        child: Wrap(
          spacing: 8,
          alignment: WrapAlignment.center,
          children: daysOfWeek
              .map((e) => InkWell(
                    onTap: () => cubit.onChangeDayOfWeek(e),
                    child: Container(
                      width: 40,
                      height: 40,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: state.selectedDayOfWeek.contains(e) ? appTheme.primaryColorV2.withValues(alpha: 0.2) : appTheme.whiteText,
                        border: Border.all(color: appTheme.borderColorV2),
                      ),
                      child: Text(
                        e.displayName,
                        style: AppStyle.bold13_5V2(color: appTheme.blackText),
                      ),
                    ),
                  ))
              .toList(),
        ),
      ),
    );
  }
}
