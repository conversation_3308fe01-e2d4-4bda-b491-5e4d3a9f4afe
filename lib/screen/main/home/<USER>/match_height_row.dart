import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that matches the height of its children in a row layout.
/// Supports dynamic content changes and works well inside ListView.
class MatchHeightRow extends StatefulWidget {
  final List<Widget> children;
  final double spacing;
  final double minHeight;
  final CrossAxisAlignment crossAxisAlignment;

  const MatchHeightRow({
    Key? key,
    required this.children,
    this.spacing = 8.0,
    this.minHeight = 0.0,
    this.crossAxisAlignment = CrossAxisAlignment.start,
  }) : super(key: key);

  /// Convenience constructor for two children (backward compatibility)
  MatchHeightRow.two({
    Key? key,
    required Widget left,
    required Widget right,
    double spacing = 8.0,
    double minHeight = 0.0,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.start,
  }) : this(
          key: key,
          children: [left, right],
          spacing: spacing,
          minHeight: minHeight,
          crossAxisAlignment: crossAxisAlignment,
        );

  @override
  State<MatchHeightRow> createState() => _MatchHeightRowState();
}

class _MatchHeightRowState extends State<MatchHeightRow> {
  final List<GlobalKey> _childKeys = [];
  double? _maxHeight;
  bool _isCalculating = false;

  @override
  void initState() {
    super.initState();
    _initializeKeys();
    WidgetsBinding.instance.addPostFrameCallback((_) => _calculateMaxHeight());
  }

  @override
  void didUpdateWidget(covariant MatchHeightRow oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Reinitialize keys if number of children changed
    if (oldWidget.children.length != widget.children.length) {
      _initializeKeys();
      _maxHeight = null; // Force recalculation
    }

    // Recalculate if children or other properties changed
    if (_shouldRecalculate(oldWidget)) {
      _maxHeight = null;
      WidgetsBinding.instance.addPostFrameCallback((_) => _calculateMaxHeight());
    }
  }

  void _initializeKeys() {
    _childKeys.clear();
    for (int i = 0; i < widget.children.length; i++) {
      _childKeys.add(GlobalKey());
    }
  }

  bool _shouldRecalculate(MatchHeightRow oldWidget) {
    // Check if any children changed (reference comparison)
    if (oldWidget.children.length != widget.children.length) return true;

    for (int i = 0; i < widget.children.length; i++) {
      if (oldWidget.children[i] != widget.children[i]) return true;
    }

    return oldWidget.minHeight != widget.minHeight;
  }

  void _calculateMaxHeight() {
    if (_isCalculating || !mounted) return;

    _isCalculating = true;

    try {
      double maxHeight = widget.minHeight;
      bool allRendered = true;

      // Check if all children are rendered and get their heights
      for (final key in _childKeys) {
        final context = key.currentContext;
        final renderBox = context?.findRenderObject() as RenderBox?;

        if (renderBox?.hasSize == true) {
          maxHeight = math.max(maxHeight, renderBox!.size.height);
        } else {
          allRendered = false;
          break;
        }
      }

      // Only update if all children are rendered and height changed significantly
      if (allRendered && (_maxHeight == null || (maxHeight - (_maxHeight ?? 0)).abs() > 0.5)) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            setState(() {
              _maxHeight = maxHeight;
            });
          }
          _isCalculating = false;
        });
      } else {
        _isCalculating = false;
      }
    } catch (e) {
      _isCalculating = false;
      // Silently handle any rendering errors
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.children.isEmpty) {
      return const SizedBox.shrink();
    }

    // First render or when recalculating: let children determine natural size
    if (_maxHeight == null) {
      return Row(
        crossAxisAlignment: widget.crossAxisAlignment,
        children: _buildNaturalSizeChildren(),
      );
    }

    // Subsequent renders: use calculated max height
    return Row(
      crossAxisAlignment: widget.crossAxisAlignment,
      children: _buildConstrainedChildren(),
    );
  }

  List<Widget> _buildNaturalSizeChildren() {
    final children = <Widget>[];

    for (int i = 0; i < widget.children.length; i++) {
      if (i > 0) {
        children.add(SizedBox(width: widget.spacing));
      }

      children.add(
        Expanded(
          child: SizeChangeNotifierWidget(
            key: _childKeys[i],
            onSizeChange: _calculateMaxHeight,
            child: widget.children[i],
          ),
        ),
      );
    }

    return children;
  }

  List<Widget> _buildConstrainedChildren() {
    final children = <Widget>[];

    for (int i = 0; i < widget.children.length; i++) {
      if (i > 0) {
        children.add(SizedBox(width: widget.spacing));
      }

      children.add(
        Expanded(
          child: SizedBox(
            height: _maxHeight,
            child: widget.children[i],
          ),
        ),
      );
    }

    return children;
  }
}

/// Widget that notifies when its child's size changes
class SizeChangeNotifierWidget extends StatefulWidget {
  final Widget child;
  final VoidCallback onSizeChange;

  const SizeChangeNotifierWidget({
    Key? key,
    required this.child,
    required this.onSizeChange,
  }) : super(key: key);

  @override
  State<SizeChangeNotifierWidget> createState() => _SizeChangeNotifierWidgetState();
}

class _SizeChangeNotifierWidgetState extends State<SizeChangeNotifierWidget> {
  Size? _previousSize;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) => _checkSize());
  }

  @override
  void didUpdateWidget(covariant SizeChangeNotifierWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.child != widget.child) {
      WidgetsBinding.instance.addPostFrameCallback((_) => _checkSize());
    }
  }

  void _checkSize() {
    if (!mounted) return;

    final context = this.context;
    final renderBox = context.findRenderObject() as RenderBox?;

    if (renderBox?.hasSize == true) {
      final currentSize = renderBox!.size;

      if (_previousSize != currentSize) {
        _previousSize = currentSize;
        widget.onSizeChange();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Use LayoutBuilder to detect constraint changes
        WidgetsBinding.instance.addPostFrameCallback((_) => _checkSize());
        return widget.child;
      },
    );
  }
}