import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/utils/assets/shadow_util.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:flutter/material.dart';
import 'package:auto_route/auto_route.dart';

class ListCard extends StatelessWidget {
  final HomeState state;
  final Color? backgroundColor;

  const ListCard({
    Key? key,
    required this.state,
    this.backgroundColor = Colors.white,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var textColor = appTheme.blackText;
    final radius = BorderRadius.circular(24.w2);
    return InkWell(
      onTap: () {
        context.pushRoute(const CheckListRoute());
      },
      borderRadius: radius,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: radius,
          color: backgroundColor,
          boxShadow: ShadowUtil.backgroundShadow,
        ),
        child: Stack(
          children: [
            Padding(
              padding: paddingV2(all: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        // Show the title and the count of items
                        '${LocaleKeys.lists.tr()} (${state.listItems.length})',
                        style: AppStyle.medium16(color: textColor),
                      ),
                      const Spacer(),
                      ImageAssetCustom(
                        imagePath: Assets.icons.iconArowFull.path,
                        width: 20,
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    height: 140,
                    child: state.listItems.isEmpty
                        ? Center(
                            child: Text(
                              LocaleKeys.no_items.tr(),
                              style: AppStyle.regular12(color: appTheme.grayV2),
                            ),
                          )
                        : ListView.builder(
                            padding: const EdgeInsets.only(top: 2),
                            shrinkWrap: true,
                            physics: const AlwaysScrollableScrollPhysics(),
                            itemCount: state.listItems.length,
                            itemBuilder: (context, i) => _ListCardItem(
                              name: state.listItems[i].name,
                              itemCount: state.listItems[i].items?.length ?? 0,
                            ),
                          ),
                  ),
                ],
              ),
            ),
            // Overlay gradient at the bottom
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: IgnorePointer(
                child: Container(
                  height: 82,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.vertical(bottom: radius.bottomLeft, top: Radius.zero),
                    gradient: const LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color.fromRGBO(255, 255, 255, 0),
                        Color.fromRGBO(255, 255, 255, 0.94),
                      ],
                      stops: [0.0, 1.0],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ListCardItem extends StatelessWidget {
  final String? name;
  final int itemCount;

  const _ListCardItem({
    Key? key,
    required this.name,
    required this.itemCount,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var textColor = appTheme.blackText;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                name ?? LocaleKeys.no_description.tr(),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
                style: AppStyle.regular12(color: appTheme.blackColor),
              ),
            ),
          ],
        ),
        Text(
          '$itemCount ${LocaleKeys.items.tr()}',
          style: AppStyle.regular10(color: textColor),
        ),
        Divider(
          color: appTheme.gray767680,
          thickness: 0.5,
        ),
      ],
    );
  }
}
