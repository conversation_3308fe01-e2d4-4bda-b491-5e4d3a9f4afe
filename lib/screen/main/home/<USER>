import 'dart:math';

import 'package:auto_route/auto_route.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:dartx/dartx_io.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/base/stream/base_stream_builder.dart';
import 'package:family_app/base/widget/cubit/base_bloc_page.dart';
import 'package:family_app/base/widget/cubit/base_bloc_provider.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/local/local_storage.dart';
import 'package:family_app/data/model/activity.dart';
import 'package:family_app/extension.dart';
import 'package:family_app/gen/assets.gen.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/chat/chat_context.dart';
import 'package:family_app/screen/main/chat/chat_parameter.dart';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/screen/main/home/<USER>';
import 'package:family_app/utils/assets/shadow_util.dart';
import 'package:family_app/utils/extension/context_ext.dart';
import 'package:family_app/utils/flash/toast.dart';
import 'package:family_app/widget/button.dart';
import 'package:family_app/widget/image/avatar.dart';
import 'package:family_app/widget/image_asset_custom.dart';
import 'package:family_app/widget/round_item_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import 'widget/home_calendar/home_calendar.dart';
import 'widget/list_card.dart';
import 'widget/task_card.dart';
import 'widget/match_height_row.dart';

@RoutePage()
class HomePage extends BaseBlocProvider<HomeState, HomeCubit> {
  const HomePage({super.key});

  @override
  Widget buildPage() => const HomeView();

  @override
  HomeCubit createCubit() => HomeCubit(
        categoryRepository: locator.get(),
        accountService: locator.get(),
        activityRepository: locator.get(),
        socketService: locator.get(),
        notificationService: locator.get(),
        familyRepository: locator.get(),
        signOutUsecase: locator.get(),
        eventRepository: locator.get(),
        threadRepository: locator.get(),
        eventService: locator.get(),
      );
}

class HomeView extends StatefulWidget {
  const HomeView({super.key});

  @override
  State<HomeView> createState() => _HomeViewState();
}

class _HomeViewState extends BaseBlocPageStateV2<HomeView, HomeState, HomeCubit> {
  double get todoCardHeight => 135.h2;

  List<DateTime> getAllDaysInMonth(DateTime date) {
    int daysInMonth = DateTime(date.year, date.month + 1, 0).day;
    List<DateTime> days = [];

    for (int i = 1; i <= daysInMonth; i++) {
      days.add(DateTime(date.year, date.month, i));
    }
    return days;
  }

  @override
  bool listenWhen(HomeState previous, HomeState current) {
    if (previous.status != current.status) {
      if (current.status == HomeStatus.logoutSuccess) {
        context.router.replaceAll([const SignInRoute()]);
        showSimpleToast(LocaleKeys.logout_successful_text.tr());
      }
    }
    return super.listenWhen(previous, current);
  }

  @override
  Widget buildBody(BuildContext context, HomeCubit cubit, HomeState state) {
    if (state.status == HomeStatus.loading) return const Center(child: CircularProgressIndicator());

    return RefreshIndicator(
      onRefresh: cubit.onRefresh,
      child: ListView(
        padding: paddingV2(vertical: 16, horizontal: 8).add(EdgeInsets.only(top: context.top, bottom: context.bottom)),
        children: [
          _buildProfileSection(context, cubit, state),
          _buildTaskSection(context, cubit, state),
          _buildCalendarAndListSection(context, cubit, state),
          _buildMemorySection(context, cubit, state),
          _buildActivityAndMessageSection(context, cubit, state),
        ],
      ),
    );
  }

  Widget _buildProfileSection(BuildContext context, HomeCubit cubit, HomeState state) {
    return Column(
      children: [
        _buildProfileRow(cubit),
        SizedBox(height: 8.h2),
        // if (state.isShowAISuggestion) _buildAiSuggestion(context, cubit, state),
        SizedBox(height: 8.h2),
      ],
    );
  }

  Widget _buildTaskSection(BuildContext context, HomeCubit cubit, HomeState state) {
    return Column(
      children: [
        _buildTaskReminderCard(state, cubit),
        SizedBox(height: 8.h2),
      ],
    );
  }

  Widget _buildCalendarAndListSection(BuildContext context, HomeCubit cubit, HomeState state) {
    return Column(
      children: [
        MatchHeightRow(
          minHeight: 150,
          spacing: 8.w2,
          children: [
            BaseStreamBuilder(
                controller: cubit.eventService.events,
                builder: (events) {
                  return HomeCalendar(
                    events: events,
                    isLoadingHolidays: state.isLoadingHolidays,
                    holidays: state.holidays,
                  );
                }),
            ListCard(state: state),
          ],
        ),
        SizedBox(height: 8.h2),
      ],
    );
  }

  Widget _buildMemorySection(BuildContext context, HomeCubit cubit, HomeState state) {
    return Column(
      children: [
        _buildMemoryCard(cubit, state),
        SizedBox(height: 8.h2),
      ],
    );
  }

  Widget _buildActivityAndMessageSection(BuildContext context, HomeCubit cubit, HomeState state) {
    return GridView.count(
      shrinkWrap: true,
      childAspectRatio: 1,
      padding: EdgeInsets.zero,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 8.w2,
      children: [_buildActivityCard(state), _buildMessageCard(state)],
    );
  }

  Widget _buildProfileRow(HomeCubit cubit) {
    var textColor = appTheme.blackText;
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      spacing: 8,
      children: [
        Flexible(
          child: InkWell(
            onTap: () => context.pushRoute(const FamilyListRoute()),
            child: Container(
              padding: padding(all: 6),
              alignment: null,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(999),
                color: appTheme.whiteText,
                boxShadow: ShadowUtil.backgroundShadow,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ClipRRect(borderRadius: BorderRadius.circular(40), child: ImageAssetCustom(imagePath: Assets.images.avatarFamily.path, width: 40)),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          cubit.accountService.account?.activeFamilyName ?? '',
                          style: AppStyle.regular12(color: textColor),
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          cubit.accountService.account?.fullName ?? '',
                          style: AppStyle.medium12(color: textColor),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        Row(
          children: [
            Container(
              padding: padding(all: 6),
              alignment: null,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(999),
                color: appTheme.lightBotColor,
              ),
              child: InkWell(
                onTap: () => {goToChatScreen()},
                child: Row(
                  children: [
                    ImageAssetCustom(
                      imagePath: Assets.icons.iconLightning.path,
                      width: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      LocaleKeys.ai_suggestion.tr(),
                      style: AppStyle.regular12(color: appTheme.whiteText),
                    ),
                  ],
                ),
              ),
            ),
            // const SizedBox(width: 5),
            // InkWell(
            //   child: RoundItemView(
            //     imagePath: Assets.icons.iconNotificaiton.path,
            //     radius: 999,
            //     viewSize: 48,
            //   ),
            // ),
            const SizedBox(width: 5),
            InkWell(
              onTap: () => context.pushRoute(const ProfileRoute()),
              child: RoundItemView(
                imagePath: Assets.icons.iconUser.path,
                radius: 999,
                viewSize: 48,
              ),
            )
          ],
        )
      ],
    );
  }

  // Widget _buildAiSuggestion(BuildContext context, HomeCubit cubit, HomeState state) {
  //   return RoundItemView(
  //       viewPadding: padding(top: 8, bottom: 9, horizontal: 12),
  //       backgroundColor: appTheme.suggestColor,
  //       radius: 20,
  //       child: Row(
  //         children: [
  //           const SizedBox(width: 0),
  //           Expanded(
  //             child: Row(
  //               crossAxisAlignment: CrossAxisAlignment.center,
  //               children: [
  //                 Container(
  //                   padding: padding(all: 6),
  //                   alignment: null,
  //                   decoration: BoxDecoration(
  //                     borderRadius: BorderRadius.circular(999),
  //                     color: appTheme.lightBotColor,
  //                   ),
  //                   child: Row(
  //                     children: [
  //                       ImageAssetCustom(
  //                         imagePath: Assets.icons.iconLightning.path,
  //                         width: 20,
  //                       ),
  //                       const SizedBox(width: 8),
  //                       Column(
  //                         crossAxisAlignment: CrossAxisAlignment.start,
  //                         children: [
  //                           Text(
  //                             LocaleKeys.ai_suggestion.tr(),
  //                             style: AppStyle.regular10(color: appTheme.whiteText),
  //                           ),
  //                         ],
  //                       )
  //                     ],
  //                   ),
  //                 ),
  //                 const SizedBox(width: 6),
  //                 Expanded(
  //                     child: AutoSizeText(LocaleKeys.plan_trip_suggestion.tr(),
  //                         style: AppStyle.regular13(color: appTheme.blackText))),
  //                 const SizedBox(width: 6),
  //                 InkWell(
  //                   onTap: () => cubit.emit(state.copyWith(isShowAISuggestion: false)),
  //                   child: Container(
  //                       padding: padding(all: 6),
  //                       decoration:
  //                           BoxDecoration(shape: BoxShape.circle, color: appTheme.suggestColor.withValues(alpha: 0.2)),
  //                       child: Assets.icons.icClose.svg(width: 12, height: 12)),
  //                 ),
  //               ],
  //             ),
  //           )
  //         ],
  //       ));
  // }

  //Blocbuilder()is used in the main buildBody already, now just get the param and use them
  Widget _buildTaskReminderCard(HomeState state, HomeCubit cubit) {
    var textColor = appTheme.blackText;

    return Container(
      margin: paddingV2(top: 8),
      padding: padding(all: 12),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: appTheme.whiteText,
        boxShadow: ShadowUtil.backgroundShadow,
      ),
      child: Column(
        children: [
          const SizedBox(height: 12),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  state.tasksDueToday.isEmpty ? LocaleKeys.no_tasks_today.tr() : LocaleKeys.tasks_count_today.tr(namedArgs: {'count': state.tasksDueToday.length.toString()}),
                  style: AppStyle.medium16(color: textColor),
                ),
              ),
              // Text(LocaleKeys.view_all.tr(),
              //     style: TextStyle(
              //       fontSize: 12,
              //       fontWeight: FontWeight.w600,
              //       color: appTheme.primaryColorV2,
              //       height: 1.4,
              //     )),
            ],
          ),
          const SizedBox(height: 12),
          if (state.tasksDueToday.isNotEmpty) ...[
            SizedBox(
                height: todoCardHeight,
                child: Stack(
                  children: [
                    CarouselSlider.builder(
                        itemCount: state.tasksDueToday.length + 1,
                        itemBuilder: (BuildContext context, int itemIndex, int pageViewIndex) {
                          if (itemIndex > state.tasksDueToday.length - 1) {
                            return Row(
                              children: [
                                if (!isViewer)
                                  InkWell(
                                    onTap: () {
                                      cubit.onCreateListItem(context, isViewer, null);
                                    },
                                    child: Ink(
                                      child: Container(
                                        width: MediaQuery.of(context).size.width * 0.15,
                                        decoration: BoxDecoration(
                                          color: appTheme.primaryColorV2.withValues(alpha: 0.2),
                                          borderRadius: BorderRadius.circular(10),
                                        ),
                                        child: Center(
                                          child: Icon(
                                            Icons.add,
                                            color: appTheme.whiteText,
                                            size: 32,
                                            weight: 700,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                              ],
                            );
                          }
                          return TaskCard.fromModel(state.tasksDueToday[itemIndex], onTap: (item) {
                            cubit.onTapTodoItem(context, item);
                          });
                        },
                        options: CarouselOptions(
                            height: todoCardHeight,
                            aspectRatio: 263 / todoCardHeight,
                            enableInfiniteScroll: false,
                            autoPlay: false,
                            enlargeCenterPage: true,
                            viewportFraction: 0.8,
                            enlargeFactor: 0,
                            initialPage: 0,
                            padEnds: false,
                            onPageChanged: (index, reason) {
                              cubit.todayIndex.value = index;
                            })),
                    Positioned(
                      right: 0,
                      child: Container(
                        width: 83,
                        height: todoCardHeight,
                        decoration: const BoxDecoration(
                            gradient: LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: [Color.fromRGBO(255, 255, 255, 0), Colors.white],
                          stops: [0.1988, 0.9578],
                        )),
                      ),
                    ),
                  ],
                )),
            const SizedBox(height: 16),
            ValueListenableBuilder(
                valueListenable: cubit.todayIndex,
                builder: (context, index, _) {
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      state.tasksDueToday.length,
                      (i) => AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        margin: paddingV2(horizontal: 4),
                        width: index == i ? 16 : 8,
                        height: 4,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(9999),
                          color: index == i ? appTheme.grayV2 : appTheme.borderColorV2,
                        ),
                      ),
                    ),
                  );
                }),
          ]

          // SizedBox(
          //   height: state.tasksDueToday.isEmpty ? 0 : 122,
          //   child: PageView.builder(
          //     controller: cubit.todayCardController,
          //     scrollDirection: Axis.horizontal,
          //     onPageChanged: (index) {
          //       cubit.onTaskCardIndexChanged(index);
          //     },
          //     itemCount: state.tasksDueToday.length,
          //     itemBuilder: (context, index) {
          //       return TaskCard.fromModel(state.tasksDueToday[index]);
          //     },
          //   ),
          // ),
          // SizedBox(
          //   height: 20,
          //   child: ListView.builder(
          //     // DOTS  INDICATOR
          //     scrollDirection: Axis.horizontal,
          //     // physics: NeverScrollableScrollPhysics(), // Disable scrolling
          //     itemCount: state.tasksDueToday.length,
          //     itemBuilder: (context, index) {
          //       return Container(
          //         margin: const EdgeInsets.symmetric(horizontal: 4),
          //         width: 8,
          //         height: 8,
          //         decoration: BoxDecoration(
          //           shape: BoxShape.circle,
          //           color: Colors.grey,
          //         ),
          //       );
          //     },
          //   ),
          // )
        ],
      ),
    );
  }

  // Widget _buildTaskReminderCard(HomeState state, HomeCubit cubit) {
  //   final radius = BorderRadius.circular(24.w2);
  //
  //   return Container(
  //     margin: paddingV2(top: 8),
  //     padding: paddingV2(all: 16),
  //     decoration: BoxDecoration(borderRadius: radius, color: Colors.white),
  //     child: Column(children: [
  //       Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
  //         Expanded(
  //           child: Text(
  //             state.tasksDueToday.isEmpty
  //                 ? LocaleKeys.no_tasks_today.tr()
  //                 : LocaleKeys.tasks_count_today.tr(namedArgs: {'count': state.tasksDueToday.length.toString()}),
  //             style: AppStyle.bold16V2(),
  //           ),
  //         ),
  //         if (state.tasksDueToday.isNotEmpty)
  //           Padding(
  //             padding: paddingV2(left: 8),
  //             child: Material(
  //               color: Colors.transparent,
  //               child: InkWell(
  //                 onTap: () {},
  //                 child: Text(LocaleKeys.view_all.tr(), style: AppStyle.bold12V2(color: appTheme.primaryColorV2)),
  //               ),
  //             ),
  //           ),
  //       ]),
  //       if (state.tasksDueToday.isNotEmpty)
  //         Builder(builder: (context) {
  //           final index = ValueNotifier(0);
  //
  //           return Column(children: [
  //             SizedBox(height: 8.h2),
  //             SizedBox(
  //               height: state.tasksDueToday.isEmpty ? 0 : 122.h2,
  //               child: Stack(children: [
  //                 PageView.builder(
  //                   controller: PageController(viewportFraction: 0.742937853),
  //                   padEnds: false,
  //                   scrollDirection: Axis.horizontal,
  //                   onPageChanged: cubit.onTaskCardIndexChanged,
  //                   itemCount: state.tasksDueToday.length,
  //                   itemBuilder: (context, index) => TaskCard.fromModel(state.tasksDueToday[index]),
  //                 ),
  //                 Positioned.fill(
  //                     left: null,
  //                     child: Container(
  //                       width: 83.w2,
  //                       decoration: const BoxDecoration(
  //                           gradient: LinearGradient(
  //                         begin: Alignment.centerLeft,
  //                         end: Alignment.centerRight,
  //                         colors: [Color.fromRGBO(255, 255, 255, 0), Colors.white],
  //                         stops: [0.1988, 0.9578],
  //                       )),
  //                     )),
  //               ]),
  //             ),
  //             SizedBox(height: 8.h2),
  //             // DOTS  INDICATOR
  //             ValueListenableBuilder(
  //               valueListenable: index,
  //               builder: (context, i, _) => Row(
  //                 mainAxisAlignment: MainAxisAlignment.center,
  //                 children: List.generate(
  //                   state.tasksDueToday.length,
  //                   (index) => AnimatedContainer(
  //                     duration: const Duration(milliseconds: 300),
  //                     margin: paddingV2(horizontal: 4),
  //                     width: i == index ? 16.w2 : 8.w2,
  //                     height: 4.h2,
  //                     decoration: BoxDecoration(
  //                       borderRadius: BorderRadius.circular(9999),
  //                       color: i == index ? appTheme.grayV2 : appTheme.borderColorV2,
  //                     ),
  //                   ),
  //                 ),
  //               ),
  //             ),
  //           ]);
  //         }),
  //     ]),
  //   );
  // }

  // List card showing the top 3 in the shopping list or todo list
  // Widget _buildListCard(HomeState state) { ... } // moved to ListCard widget

  Widget _buildMemoryCard(HomeCubit cubit, HomeState state) {
    return InkWell(
      onTap: () => context.pushRoute(const MemoriesRoute()).then(
            (value) => cubit.onRefreshMemoryCard(),
          ),
      child: Container(
        height: 200,
        alignment: null,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
        ),
        child: Stack(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(10.0),
              child: _buildMemoryImage(context, cubit, state),
            ),
            //  _buildMemoryCaption(context, cubit, state),
          ],
        ),
      ),
    );
  }

  // Widget _buildMemoryCaption(BuildContext context, HomeCubit cubit, HomeState state) { ... } // removed unused function

  Widget _buildMemoryImage(BuildContext context, HomeCubit cubit, HomeState state) {
    if (state.latestMemory?.uuid == null) {
      return _buildEmptyMemory(context, cubit, state);
    }

    List<String> allPhotoUrls = [];

    state.allMemories?.forEach((element) {
      element.files?.forEach((file) {
        if (file.fileUrl != null) {
          allPhotoUrls.add(file.fileUrl!); // Add each file URL to the list
        }
      });
    });

    return SizedBox(
      height: state.allMemories!.isEmpty ? 0 : 200,
      child: CarouselSlider.builder(
        itemCount: allPhotoUrls.length,
        itemBuilder: (context, index, realIndex) {
          return CachedNetworkImage(
            imageUrl: allPhotoUrls[index],
            height: 200,
            width: double.infinity,
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              height: 200,
              width: double.infinity,
              color: Colors.grey,
              child: const Center(child: CircularProgressIndicator()),
            ),
            errorWidget: (context, url, error) => Container(
              height: 200,
              width: double.infinity,
              color: Colors.grey,
              child: const Icon(Icons.error, color: Colors.red),
            ),
          );
        },
        options: CarouselOptions(
          autoPlay: true,
          autoPlayInterval: const Duration(seconds: 3),
          height: 200,
          enlargeCenterPage: true,
          viewportFraction: 1.0,
        ),
      ),
    );
  }

  Widget _buildEmptyMemory(BuildContext context, HomeCubit cubit, HomeState state) {
    return Container(
      height: 200,
      alignment: null,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: appTheme.whiteText,
      ),
      child: const Center(
        child: Text(
          "Create your first memory now!",
          // LocaleKeys.create_your_first_memory_now.tr(),
          style: TextStyle(
            color: Colors.black,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildActivityCard(HomeState state) {
    final textColor = appTheme.blackText;
    final latestActivity = state.activityList.firstOrNull;
    final ValueNotifier<List<ActivityModel>> activityListNotifier = ValueNotifier(state.activityList);
    return InkWell(
        onTap: () => context.pushRoute(const ActivityHomeRoute()),
        child: Container(
            height: 190,
            padding: padding(all: 12),
            alignment: null,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              image: DecorationImage(image: CachedNetworkImageProvider(latestActivity?.imagePath ?? ''), fit: BoxFit.cover
                  // colorFilter: ColorFilter.mode(Colors.transparent.withOpacity(0.5), BlendMode.srcIn),
                  ),
            ),
            child: Column(children: [
              const SizedBox(height: 12),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Spacer(),
                  Transform.rotate(
                    angle: pi,
                    child: Assets.icons.arrowRight.svg(width: 20.w, height: 20.w, colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn)),
                  ),
                  const SizedBox(width: 5),
                  SvgPicture.asset(Assets.icons.arrowRight.path)
                ],
              ),
              const Spacer(),
              Container(
                  decoration: BoxDecoration(
                    color: Colors.white, // Set the background color to white
                    borderRadius: BorderRadius.circular(20), // Optional: Add rounded corners
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: Row(mainAxisAlignment: MainAxisAlignment.start, crossAxisAlignment: CrossAxisAlignment.start, children: [
                    ValueListenableBuilder<List<ActivityModel>>(
                        valueListenable: activityListNotifier,
                        builder: (context, value, child) {
                          return Expanded(
                              child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(latestActivity?.name ?? LocaleKeys.no_activity.tr(), style: AppStyle.bold14(color: textColor), maxLines: 2, overflow: TextOverflow.ellipsis),
                              // const SizedBox(height: 3),
                              Text(
                                latestActivity?.shortFromTo() ?? "",
                                style: AppStyle.regular13(color: textColor),
                              ),
                            ],
                          ));
                        })
                  ]))
            ])));
  }

  Widget _buildMessageCard(HomeState state) {
    Widget buildFakeAvatar({color = Colors.white, double? size, Widget? child}) {
      return CircleAvatar(backgroundColor: color, radius: size == null ? null : size / 2, child: child);
    }

    Widget buildItem(int i) {
      final e = state.messages.elementAtOrNull(i);

      if (e == null || (e.members?.isEmpty ?? true) || e.members!.length == 1) return buildFakeAvatar(color: appTheme.backgroundV2);

      return LayoutBuilder(
        builder: (context, constraints) {
          final members = e.members!.where((e) => e.uuid != accountService.account?.uuid);

          if (members.length == 1) return Avatar(null, name: members.first.fullName, size: constraints.maxHeight);

          final size = constraints.maxHeight * 0.6;

          if (members.length == 2)
            return Stack(children: [
              Positioned(top: 0, left: 0, child: Avatar(null, name: members.elementAt(0).fullName, size: size)),
              Positioned(bottom: 1.h2, right: 1.w2, child: buildFakeAvatar(size: size)),
              Positioned(bottom: 0, right: 0, child: Avatar(null, name: members.elementAt(1).fullName, size: size)),
            ]);

          return Stack(alignment: Alignment.centerRight, children: [
            Positioned(top: 0, left: 0, child: Avatar(null, name: members.elementAt(0).fullName, size: size)),
            Positioned(bottom: -1.35.h2, left: -1.35.w2, child: buildFakeAvatar(size: size + 2.7.w2)),
            Positioned(bottom: 0, left: 0, child: Avatar(null, name: members.elementAt(1).fullName, size: size)),
            Positioned(right: -1.35.w2, child: buildFakeAvatar(size: size + 2.7.w2)),
            Positioned(
              right: 0,
              child: members.length == 3
                  ? Avatar(null, name: members.elementAt(2).fullName, size: size)
                  : buildFakeAvatar(
                      color: const Color.fromRGBO(78, 70, 180, 0.12),
                      size: size,
                      child: Text('+${members.length - 2}', style: AppStyle.bold13_5V2(color: appTheme.primaryColorV2)),
                    ),
            ),
          ]);
        },
      );
    }

    return Button(
      onTap: () => context.pushRoute(const ThreadRoute()),
      borderRadius: BorderRadius.circular(24.w2),
      color: Colors.white,
      padding: paddingV2(all: 16, bottom: 18.5),
      child: state.messages.isEmpty
          ? Assets.icons.messageEmpty.svg(width: 157.w2)
          : Column(children: [
              Row(children: [
                Text(LocaleKeys.messages.tr(), style: AppStyle.bold16V2()),
                SizedBox(width: 4.w2),
                if (state.totalUnreadMessages > 0)
                  Ink(
                    decoration: BoxDecoration(color: appTheme.errorV2, borderRadius: BorderRadius.circular(4.w2)),
                    padding: paddingV2(horizontal: 4),
                    child: Text('+${state.totalUnreadMessages > 99 ? 99 : state.totalUnreadMessages}', style: AppStyle.bold9V2(color: Colors.white)),
                  ),
              ]),
              SizedBox(height: 10.h2),
              Expanded(
                child: LayoutBuilder(
                  builder: (context, constraints) => SizedBox(
                    width: constraints.maxHeight - 3.5.w2,
                    height: constraints.maxHeight,
                    child: GridView.count(
                      padding: EdgeInsets.zero,
                      physics: const NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                      crossAxisCount: 2,
                      childAspectRatio: 1,
                      crossAxisSpacing: 10.w2,
                      mainAxisSpacing: 12.5.h2,
                      children: List.generate(max(state.messages.length, 4), buildItem),
                    ),
                  ),
                ),
              ),
            ]),
    );
  }

  Future<void> goToChatScreen() async {
    LocalStorage localStorage = locator.get();
    final token = await localStorage.accessToken();
    if (token!.isEmpty) {
      await localStorage.clear();
      context.replaceRoute(const AuthRoute());
    } else {
      context.pushRoute(ChatRoute(parameter: ChatParameter(chatContext: ChatContext.getGeneralChatContext(token))));
    }
  }

// Widget _buildAiSuggestion() {
//   return Container(
//     margin: paddingV2(top: 8),
//     padding: paddingV2(all: 8),
//     decoration:
//         BoxDecoration(color: const Color.fromRGBO(78, 70, 180, 0.12), borderRadius: BorderRadius.circular(24.w2)),
//     child: Row(children: [
//       Container(
//         padding: paddingV2(all: 4, right: 8),
//         decoration: BoxDecoration(borderRadius: BorderRadius.circular(16.w2), color: appTheme.primaryColorV2),
//         child: Row(children: [
//           SvgPicture.asset(Assets.icons.iconLightning.path, width: 16.w2),
//           Text(LocaleKeys.ai_suggestion.tr(), style: AppStyle.bold10V2(color: appTheme.whiteText))
//         ]),
//       },
//       SizedBox(width: 8.w2),
//       Expanded(
//           child: Text(LocaleKeys.plan_trip_suggestion.tr(),
//               style: AppStyle.regular14V2(), overflow: TextOverflow.ellipsis, maxLines: 1)),
//       SizedBox(width: 8.w2),
//       ButtonIcon(Assets.icons.icClose.path, () {}, size: 24, sizeIcon: 10, bg: const Color.fromRGBO(0, 0, 0, 0.04)),
//     ]),
//   );
// }

// Widget _buildListCard(HomeState state) {
//   var textColor = appTheme.blackText;
//   final radius = BorderRadius.circular(24.w2);
//
//   return InkWell(
//     onTap: () => context.pushRoute(const CheckListRoute()),
//     borderRadius: radius,
//     child: Stack(children: [
//       Ink(
//         padding: paddingV2(all: 16, bottom: 0),
//         decoration: BoxDecoration(borderRadius: radius, color: Colors.white),
//         child: Column(children: [
//           // LIST HEADING
//           Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
//             Expanded(
//               child: Text(LocaleKeys.list_count.tr(namedArgs: {'count': '${state.listItems.length}'}),
//                   style: AppStyle.medium16(color: textColor), maxLines: 1, overflow: TextOverflow.ellipsis),
//             ),
//             SvgPicture.asset(Assets.icons.iconArowFull.path, width: 16.w2),
//           ]),
//           SizedBox(height: 10.h2),
//           // LIST ITEMS
//           Expanded(
//             child: ListView.separated(
//               primary: false,
//               physics: const NeverScrollableScrollPhysics(),
//               separatorBuilder: (context, i) => Padding(
//                 padding: paddingV2(vertical: 2),
//                 child: Ink(
//                   color: appTheme.borderColorV2,
//                   height: 1.h2,
//                 ),
//               ),
//               itemCount: min(4, state.listItems.length),
//               itemBuilder: (context, i) {
//                 return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
//                   Text(state.listItems[i].name ?? LocaleKeys.no_description.tr(),
//                       overflow: TextOverflow.ellipsis, maxLines: 1, style: AppStyle.regular12V2()),
//                   Text('${state.listItems[i].items?.length} ${LocaleKeys.items.tr()}',
//                       style: AppStyle.regular10V2(color: appTheme.grayV2)),
//                 ]);
//               },
//               padding: EdgeInsets.zero,
//             ),
//           )
//         ]),
//       ),
//       Positioned.fill(
//         top: null,
//         child: AspectRatio(
//           aspectRatio: 2.30487805,
//           child: DecoratedBox(
//             decoration: BoxDecoration(
//               borderRadius: radius,
//               gradient: LinearGradient(
//                 begin: Alignment.topCenter,
//                 end: Alignment.bottomCenter,
//                 colors: [Colors.white.withValues(alpha: 0), Colors.white],
//                 stops: [0.0, 0.9399],
//               ),
//             ),
//           ),
//         ),
//       ),
//     ]),
//   );
// }

// Widget _buildMemoryCard(HomeCubit cubit, HomeState state) {
//   onTap() => context.pushRoute(const MemoriesRoute()).then((value) => cubit.onRefreshMemoryCard());
//
//   if (state.latestMemory?.uuid == null || (state.allMemories?.isEmpty ?? true))
//     return Button(
//       onTap: onTap,
//       height: 200.h2,
//       color: Colors.white,
//       borderRadius: BorderRadius.circular(24.w2),
//       child: Center(child: Text(LocaleKeys.create_memory.tr(), style: AppStyle.bold16V2())),
//     );
//
//   final height = 200.h2;
//
//   Widget itemBuilder(BuildContext context, int index, int realIndex) {
//     final e = state.allMemories!.elementAt(index);
//
//     return Stack(children: [
//       ClipRRect(
//         borderRadius: BorderRadius.circular(24.w2),
//         child: CachedNetworkImage(
//           imageUrl: e.files?.firstOrNullWhere((e) => e.fileUrl != null)?.fileUrl ?? '',
//           height: height,
//           width: double.infinity,
//           fit: BoxFit.cover,
//           placeholder: (context, _) => Container(
//             width: double.infinity,
//             color: Colors.grey,
//             child: const Center(child: CircularProgressIndicator()),
//           ),
//           errorWidget: (context, _, __) => Container(
//             width: double.infinity,
//             color: Colors.grey,
//             child: const Icon(Icons.error, color: Colors.red),
//           ),
//         ),
//       ),
//       Positioned(
//         top: 0,
//         right: 16.w2,
//         bottom: 16.h2,
//         left: 16.w2,
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.end,
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Text(e.caption ?? '', style: AppStyle.bold14V2(color: Colors.white)),
//             Text(LocaleKeys.by_author.tr(namedArgs: {'author': e.uuid ?? ''}),
//                 style: AppStyle.regular12V2(color: appTheme.backgroundV2)),
//           ],
//         ),
//       ),
//     ]);
//   }
//
//   return GestureDetector(
//     onTap: onTap,
//     child: Container(
//       height: height,
//       clipBehavior: Clip.hardEdge,
//       decoration: BoxDecoration(borderRadius: BorderRadius.circular(24.w2)),
//       child: CarouselSlider.builder(
//         itemCount: state.allMemories!.length,
//         itemBuilder: itemBuilder,
//         options: CarouselOptions(
//           autoPlay: true,
//           autoPlayInterval: const Duration(seconds: 3),
//           height: height,
//           enlargeCenterPage: true,
//           viewportFraction: 1.0,
//         ),
//       ),
//     ),
//   );
// }
}

// ...MatchHeightRow moved to widget/match_height_row.dart...
