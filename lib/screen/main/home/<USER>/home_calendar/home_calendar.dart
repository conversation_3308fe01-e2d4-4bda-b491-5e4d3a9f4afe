import 'package:auto_route/auto_route.dart';
import 'package:dartx/dartx_io.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/service/calendar_service.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/event.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/utils/calendar.dart';
import 'package:family_app/widget/button.dart';
import 'package:flutter/material.dart';

import 'home_calendar_day_cell.dart';
import 'utils.dart';

class HomeCalendar extends StatelessWidget {
  final List<EventModels> events;
  final bool? isLoadingHolidays;
  final Map<String, String>? holidays;

  const HomeCalendar({
    super.key,
    required this.events,
    this.isLoadingHolidays = false,
    required this.holidays,
  });

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    final startOfWeekMonday = CalendarService.instance.isStartOfWeekMonday();
    final daysOfWeek = CalendarUtils.getDaysOfWeek(startOfWeekMonday);
    final firstWeekday = now.firstDayOfMonth.weekday;
    final daysInMonth = DateTime(now.year, now.month + 1, 0).day;
    final radius = BorderRadius.circular(24);

    final dayWidgets = <Widget>[];
    dayWidgets.addAll(
      HomeCalendarUtils.buildDaysOfWeekHeaders(
        daysOfWeek,
        HomeCalendarUtils.getStyle,
        appTheme.grayA8Color,
      ),
    );
    dayWidgets.addAll(
      HomeCalendarUtils.buildEmptyCellsForFirstDay(firstWeekday, startOfWeekMonday),
    );
    dayWidgets.addAll(_buildDayCells(now, daysInMonth, holidays));

    return Button(
      borderRadius: radius,
      onTap: () => context.pushRoute(const CalendarRoute()),
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(DateFormat('MMMM').format(now), style: AppStyle.bold16V2()),
              if (isLoadingHolidays ?? false)
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 10),
          GridView.count(
            padding: EdgeInsets.zero,
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            crossAxisCount: 7,
            crossAxisSpacing: 7.5,
            mainAxisSpacing: 4,
            children: dayWidgets,
          ),
        ],
      ),
    );
  }

  List<Widget> _buildDayCells(DateTime now, int daysInMonth, Map<String, String>? holidays) {
    List<Widget> widgets = [];
    for (int day = 1; day <= daysInMonth; day++) {
      final currentDate = DateTime(now.year, now.month, day);
      final isToday = day == now.day;
      final holidayKey = '${now.month}-$day';
      final isHoliday = holidays?.containsKey(holidayKey) ?? false;
      final holidayName = isHoliday ? holidays![holidayKey] : null;
      final bgColor = isToday ? appTheme.primaryColorV2 : Colors.transparent;
      final textColor = isToday
          ? Colors.white
          : isHoliday
              ? appTheme.orangeColorV2
              : Colors.black;

      final eventsOnDate = HomeCalendarUtils.getEventsForDate(currentDate, events);

      widgets.add(
        HomeCalendarDayCell(
          day: day,
          isToday: isToday,
          isHoliday: isHoliday,
          holidayName: holidayName,
          bgColor: bgColor,
          textColor: textColor,
          getStyle: HomeCalendarUtils.getStyle,
          eventsOnDate: eventsOnDate,
          getEventColor: (event) => HomeCalendarUtils.getEventColor(event, appTheme.primaryColorV2),
        ),
      );
    }
    return widgets;
  }
}
