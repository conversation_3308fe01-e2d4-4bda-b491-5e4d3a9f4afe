import 'package:flutter/material.dart';
import 'package:family_app/data/model/event.dart';

class HomeCalendarDayCell extends StatelessWidget {
  final int day;
  final bool isToday;
  final bool isHoliday;
  final String? holidayName;
  final Color bgColor;
  final Color textColor;
  final TextStyle Function(double, Color) getStyle;
  final List<EventModels> eventsOnDate;
  final Color Function(EventModels) getEventColor;

  const HomeCalendarDayCell({
    super.key,
    required this.day,
    required this.isToday,
    required this.isHoliday,
    required this.holidayName,
    required this.bgColor,
    required this.textColor,
    required this.getStyle,
    required this.eventsOnDate,
    required this.getEventColor,
  });

  @override
  Widget build(BuildContext context) {
    final dayWidget = LayoutBuilder(
      builder: (context, constraints) => Tooltip(
        message: holidayName ?? (isToday ? 'Today' : ''),
        textStyle: const TextStyle(fontSize: 12, color: Colors.white),
        decoration: BoxDecoration(
          color: Colors.black87,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Container(
          width: double.infinity,
          height: double.infinity,
          alignment: Alignment.center,
          child: Container(
            width: constraints.maxWidth * 1,
            height: constraints.maxHeight * 1,
            decoration: BoxDecoration(
              color: bgColor,
              shape: BoxShape.circle,
            ),
            alignment: Alignment.center,
            child: Text(
              "$day",
              style: getStyle(
                isToday ? (constraints.maxHeight * 0.5).clamp(0, constraints.maxWidth * 0.5) : (constraints.maxHeight * 0.63).clamp(0, constraints.maxWidth * 0.63),
                textColor,
              ),
            ),
          ),
        ),
      ),
    );

    if (eventsOnDate.isEmpty) {
      return dayWidget;
    } else {
      Widget createDot(Color color) => Container(
            width: 3,
            height: 3,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          );

      Widget dotsRow = Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          for (int i = 0; i < (eventsOnDate.length < 3 ? eventsOnDate.length : 3); i++)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 1),
              child: createDot(getEventColor(eventsOnDate[i])),
            ),
        ],
      );

      return Stack(
        alignment: Alignment.bottomCenter,
        children: [
          dayWidget,
          Positioned(
            bottom: 0,
            child: dotsRow,
          ),
        ],
      );
    }
  }
}
