import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/data/model/event.dart';
import 'package:flutter/widgets.dart';

import 'package:family_app/utils/calendar.dart';

class HomeCalendarUtils {
  static TextStyle getStyle(double size, Color color) {
    return AppStyle.bold14V2().copyWith(fontSize: size, color: color);
  }

  static List<String> getDaysOfWeek(bool startOfWeekMonday) {
    return CalendarUtils.getDaysOfWeek(startOfWeekMonday);
  }

  static List<Widget> buildDaysOfWeekHeaders(List<String> daysOfWeek, TextStyle Function(double, Color) getStyle, Color headerColor) {
    return daysOfWeek
        .map((day) => Center(
              child: LayoutBuilder(builder: (context, constraints) => Text(day, style: getStyle(constraints.maxHeight * 0.75, headerColor))),
            ))
        .toList();
  }

  static List<Widget> buildEmptyCellsForFirstDay(int firstWeekday, bool startOfWeekMonday) {
    int emptyCells = startOfWeekMonday ? (firstWeekday - 1) % 7 : firstWeekday % 7;
    return List.generate(emptyCells, (_) => const SizedBox.shrink());
  }

  static List<EventModels> getEventsForDate(DateTime date, List<EventModels> events) {
    final dateLocal = DateTime(date.year, date.month, date.day);
    return events.where((event) {
      final startStr = event.fromDate;
      final endStr = event.toDate;
      if (startStr == null || endStr == null) return false;
      final start = DateTime.tryParse(startStr)?.toLocal();
      final end = DateTime.tryParse(endStr)?.toLocal();
      if (start == null || end == null) return false;
      final startLocal = DateTime(start.year, start.month, start.day);
      final endLocal = DateTime(end.year, end.month, end.day);
      return (dateLocal.isAtSameMomentAs(startLocal) || dateLocal.isAtSameMomentAs(endLocal) || (dateLocal.isAfter(startLocal) && dateLocal.isBefore(endLocal)));
    }).toList();
  }

  static Color getEventColor(EventModels event, Color defaultColor) {
    if (event.color != null && event.color!.isNotEmpty) {
      try {
        return Color(int.parse('0xFF${event.color!.replaceFirst('#', '')}'));
      } catch (e) {}
    }
    return defaultColor;
  }
}
